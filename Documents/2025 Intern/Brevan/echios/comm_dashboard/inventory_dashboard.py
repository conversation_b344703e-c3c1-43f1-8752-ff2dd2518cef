"""
Global Liquids Inventory Data Tracking Dashboard

A dynamic Dash application for tracking global liquids inventory data with:
- Interactive drill-down capabilities
- 5-year historical range visualization
- Week-of-year percentile ribbon
- Anomaly detection and flagging
"""

import dash
from dash import dcc, html, Input, Output, callback
import plotly.graph_objects as go
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Import our custom functions
from inventory_dashboard_functions import InventoryDataProcessor, create_main_chart, create_percentile_trend_chart, create_anomaly_flags, get_summary_stats, create_cross_geographic_chart, create_weekly_change_chart

# Initialize the data processor with correct data path
processor = InventoryDataProcessor(data_path='data/')

# Initialize Dash app
app = dash.Dash(__name__)
app.title = "Global Liquids Inventory Dashboard"

# Define the layout
app.layout = html.Div([
    # Header
    html.Div([
        html.H1("Global Liquids Inventory Tracking Dashboard", 
                style={'textAlign': 'center', 'color': '#2c3e50', 'marginBottom': '30px'}),
    ], style={'padding': '20px'}),

    # Summary Statistics at the very top
    html.Div(id='summary-stats', style={'padding': '20px', 'margin': '20px'}),

    # Main Dashboard Section with Side-by-Side Layout
    html.Div([
        # Left Panel - Main Controls
        html.Div([
            html.H3("📊 Main Dashboard Controls", style={'color': '#34495e', 'marginBottom': '15px'}),

            # Product Type
            html.Div([
                html.Label("Product Type:", style={'fontWeight': 'bold', 'color': '#2c3e50', 'fontSize': '14px'}),
                dcc.Dropdown(
                    id='product-type-dropdown',
                    options=[
                        {'label': '🛢️ All Products', 'value': 'all'},
                        {'label': '⚫ Crude Oil', 'value': 'crude_oil'},
                        {'label': '🔵 Total Products', 'value': 'total_products'},
                        {'label': '🟢 Gasoline', 'value': 'gasoline'},
                        {'label': '🟡 Distillates', 'value': 'distillates'}
                    ],
                    value='all',
                    style={'marginTop': '5px', 'marginBottom': '15px'}
                )
            ]),

            # Geography
            html.Div([
                html.Label("Geography:", style={'fontWeight': 'bold', 'color': '#2c3e50', 'fontSize': '14px'}),
                dcc.Dropdown(
                    id='geography-dropdown',
                    options=[
                        {'label': '🌍 Global', 'value': 'all'},
                        {'label': '🇺🇸 United States', 'value': 'us'},
                        {'label': '🇪🇺 Europe', 'value': 'europe'},
                        {'label': '🌏 Asia', 'value': 'asia'}
                    ],
                    value='all',
                    style={'marginTop': '5px', 'marginBottom': '15px'}
                )
            ]),

            # Storage Type
            html.Div([
                html.Label("Storage Type:", style={'fontWeight': 'bold', 'color': '#2c3e50', 'fontSize': '14px'}),
                dcc.Dropdown(
                    id='storage-type-dropdown',
                    options=[
                        {'label': '🌍 All Storage', 'value': 'all'},
                        {'label': '🏭 Land Storage', 'value': 'land'},
                        {'label': '🚢 Oil on Water', 'value': 'water'},
                        {'label': '⚓ Floating Storage', 'value': 'floating'}
                    ],
                    value='all',
                    style={'marginTop': '5px', 'marginBottom': '15px'}
                )
            ]),

            # Years
            html.Div([
                html.Label("Years to Show:", style={'fontWeight': 'bold', 'color': '#2c3e50', 'fontSize': '14px'}),
                dcc.Checklist(
                    id='years-checklist',
                    options=[
                        {'label': '2017', 'value': 2017},
                        {'label': '2018', 'value': 2018},
                        {'label': '2019', 'value': 2019},
                        {'label': '2020', 'value': 2020},
                        {'label': '2021', 'value': 2021},
                        {'label': '2022', 'value': 2022},
                        {'label': '2023', 'value': 2023},
                        {'label': '2024', 'value': 2024},
                        {'label': '2025', 'value': 2025}
                    ],
                    value=[2025],
                    style={'marginTop': '5px', 'marginBottom': '15px'}
                )
            ]),

            # 3-Month Correlation Analysis
            html.Div([
                html.Label("📊 3-Month Correlation Ranking:", style={'fontWeight': 'bold', 'color': '#2c3e50', 'fontSize': '14px'}),
                html.Div(id='correlation-ranking', style={'marginTop': '10px', 'padding': '10px', 'backgroundColor': '#f8f9fa', 'borderRadius': '5px', 'fontSize': '12px'})
            ]),

            # Column Selection
            html.Div([
                html.Label("Data Column:", style={'fontWeight': 'bold', 'color': '#2c3e50', 'fontSize': '14px'}),
                dcc.Dropdown(
                    id='column-dropdown',
                    placeholder="Select a data column...",
                    style={'marginTop': '5px'}
                )
            ])

        ], style={'width': '25%', 'display': 'inline-block', 'verticalAlign': 'top',
                 'padding': '20px', 'backgroundColor': '#ecf0f1', 'margin': '20px',
                 'borderRadius': '10px', 'marginRight': '0px'}),

        # Right Panel - Main Chart Only
        html.Div([
            html.Div([
                html.H3("📈 2025 Projection with Historical Comparison", style={'color': '#34495e'}),
                html.P("Light blue shading: 2025 projection range | Dotted lines: Historical seasonal patterns overlaid on 2025 timeline for easy week-by-week comparison",
                       style={'color': '#7f8c8d', 'fontStyle': 'italic', 'marginBottom': '15px'}),
                dcc.Graph(id='main-chart', style={'height': '500px'})
            ], style={'padding': '20px', 'backgroundColor': '#ffffff', 'borderRadius': '10px', 'boxShadow': '0 2px 4px rgba(0,0,0,0.1)'})

        ], style={'width': '70%', 'display': 'inline-block', 'verticalAlign': 'top', 'margin': '20px', 'marginLeft': '0px'})

    ], style={'marginBottom': '20px', 'display': 'flex', 'alignItems': 'flex-start'}),

    # Additional Charts Below Main Chart (Full Width)
    html.Div([
        # Weekly Change Chart Section with Side-by-Side Layout
        html.Div([
            # Left Panel - Weekly Change Controls
            html.Div([
                html.H3("📊 Weekly Change Analysis Controls", style={'color': '#34495e', 'marginBottom': '15px'}),

                # Product Type for Weekly Change
                html.Div([
                    html.Label("Product Type:", style={'fontWeight': 'bold', 'color': '#2c3e50', 'fontSize': '14px'}),
                    dcc.Dropdown(
                        id='weekly-change-product-dropdown',
                        options=[
                            {'label': '🛢️ All Products', 'value': 'all'},
                            {'label': '⚫ Crude Oil', 'value': 'crude_oil'},
                            {'label': '🔵 Total Products', 'value': 'total_products'},
                            {'label': '🟢 Gasoline', 'value': 'gasoline'},
                            {'label': '🟡 Distillates', 'value': 'distillates'}
                        ],
                        value='all',
                        style={'marginTop': '5px', 'marginBottom': '15px'}
                    )
                ]),

                # Geography for Weekly Change
                html.Div([
                    html.Label("Geography:", style={'fontWeight': 'bold', 'color': '#2c3e50', 'fontSize': '14px'}),
                    dcc.Dropdown(
                        id='weekly-change-geography-dropdown',
                        options=[
                            {'label': '🌍 Global', 'value': 'all'},
                            {'label': '🇺🇸 United States', 'value': 'us'},
                            {'label': '🇪🇺 Europe', 'value': 'europe'},
                            {'label': '🌏 Asia', 'value': 'asia'}
                        ],
                        value='all',
                        style={'marginTop': '5px', 'marginBottom': '15px'}
                    )
                ]),

                # Storage Type for Weekly Change
                html.Div([
                    html.Label("Storage Type:", style={'fontWeight': 'bold', 'color': '#2c3e50', 'fontSize': '14px'}),
                    dcc.Dropdown(
                        id='weekly-change-storage-dropdown',
                        options=[
                            {'label': '🌍 All Storage', 'value': 'all'},
                            {'label': '🏭 Land Storage', 'value': 'land'},
                            {'label': '🚢 Oil on Water', 'value': 'water'},
                            {'label': '⚓ Floating Storage', 'value': 'floating'}
                        ],
                        value='all',
                        style={'marginTop': '5px', 'marginBottom': '15px'}
                    )
                ]),

                # Years for Weekly Change
                html.Div([
                    html.Label("Years to Show:", style={'fontWeight': 'bold', 'color': '#2c3e50', 'fontSize': '14px'}),
                    dcc.Checklist(
                        id='weekly-change-years-checklist',
                        options=[
                            {'label': '2017', 'value': 2017},
                            {'label': '2018', 'value': 2018},
                            {'label': '2019', 'value': 2019},
                            {'label': '2020', 'value': 2020},
                            {'label': '2021', 'value': 2021},
                            {'label': '2022', 'value': 2022},
                            {'label': '2023', 'value': 2023},
                            {'label': '2024', 'value': 2024},
                            {'label': '2025', 'value': 2025}
                        ],
                        value=[2025],
                        style={'marginTop': '5px', 'marginBottom': '15px'}
                    )
                ]),

                # 3-Month Correlation Analysis for Weekly Change
                html.Div([
                    html.Label("📊 3-Month Correlation Ranking:", style={'fontWeight': 'bold', 'color': '#2c3e50', 'fontSize': '14px'}),
                    html.Div(id='weekly-change-correlation-ranking', style={'marginTop': '10px', 'padding': '10px', 'backgroundColor': '#f8f9fa', 'borderRadius': '5px', 'fontSize': '12px'})
                ]),

                # Column Selection for Weekly Change
                html.Div([
                    html.Label("Data Column:", style={'fontWeight': 'bold', 'color': '#2c3e50', 'fontSize': '14px'}),
                    dcc.Dropdown(
                        id='weekly-change-column-dropdown',
                        placeholder="Select a data column...",
                        style={'marginTop': '5px'}
                    )
                ])

            ], style={'width': '25%', 'display': 'inline-block', 'verticalAlign': 'top',
                     'padding': '20px', 'backgroundColor': '#f8f9fa', 'margin': '20px',
                     'borderRadius': '10px', 'marginRight': '0px'}),

            # Right Panel - Weekly Change Chart
            html.Div([
                html.Div([
                    html.H3("📈 2025 Weekly Changes with Historical Comparison", style={'color': '#34495e'}),
                    html.P("Green markers: Inventory increases | Red markers: Inventory decreases | Shows week-over-week changes compared to historical patterns",
                           style={'color': '#7f8c8d', 'fontStyle': 'italic', 'marginBottom': '15px'}),
                    dcc.Graph(id='weekly-change-chart', style={'height': '500px'})
                ], style={'padding': '20px', 'backgroundColor': '#ffffff', 'borderRadius': '10px', 'boxShadow': '0 2px 4px rgba(0,0,0,0.1)'})

            ], style={'width': '70%', 'display': 'inline-block', 'verticalAlign': 'top', 'margin': '20px', 'marginLeft': '0px'})

        ], style={'marginBottom': '20px', 'display': 'flex', 'alignItems': 'flex-start'}),

        # Weekly Percentile Trend Chart
        html.Div([
            html.H3("📈 2025 Weekly Percentile Trend Analysis", style={'color': '#34495e'}),
            html.P("Shows where 2025 weekly inventory levels rank against historical patterns. Lower percentiles indicate below-average inventory levels.",
                   style={'color': '#7f8c8d', 'fontStyle': 'italic', 'marginBottom': '15px'}),
            dcc.Graph(id='percentile-trend', style={'height': '400px'})
        ], style={'padding': '20px', 'backgroundColor': '#ffffff', 'borderRadius': '10px', 'boxShadow': '0 2px 4px rgba(0,0,0,0.1)', 'margin': '20px', 'marginBottom': '20px'}),

        # Anomaly Flags
        html.Div([
            html.H3("🚩 Anomaly Flags", style={'color': '#34495e'}),
            html.P("Automatic highlighting when current week's level is in the top/bottom 10% of its 5-year distribution",
                   style={'color': '#7f8c8d', 'fontStyle': 'italic'}),
            dcc.Graph(id='anomaly-flags', style={'height': '100px'})
        ], style={'padding': '20px', 'backgroundColor': '#ffffff', 'borderRadius': '10px', 'boxShadow': '0 2px 4px rgba(0,0,0,0.1)', 'margin': '20px'})
    ]),

    # Cross-Geographic Analysis Section with Side-by-Side Layout
    html.Div([
        # Left Panel - Cross-Geographic Controls
        html.Div([
            html.H3("🌍 Cross-Geographic Controls", style={'color': '#34495e', 'marginBottom': '15px'}),
            html.P("Compare inventory levels between regions", style={'color': '#7f8c8d', 'marginBottom': '15px', 'fontSize': '12px'}),

            # Primary Geography
            html.Div([
                html.Label("Primary Geography:", style={'fontWeight': 'bold', 'color': '#2c3e50', 'fontSize': '14px'}),
                dcc.Dropdown(
                    id='primary-geography-dropdown',
                    options=[
                        {'label': '🇺🇸 United States', 'value': 'us'},
                        {'label': '🇪🇺 Europe (ARA)', 'value': 'europe'},
                        {'label': '🌏 Asia (Singapore+)', 'value': 'asia'}
                    ],
                    value='us',
                    style={'marginTop': '5px', 'marginBottom': '15px'}
                )
            ]),

            # Comparison Geography
            html.Div([
                html.Label("Comparison Geography:", style={'fontWeight': 'bold', 'color': '#2c3e50', 'fontSize': '14px'}),
                dcc.Dropdown(
                    id='comparison-geography-dropdown',
                    options=[
                        {'label': '🇺🇸 United States', 'value': 'us'},
                        {'label': '🇪🇺 Europe (ARA)', 'value': 'europe'},
                        {'label': '🌏 Asia (Singapore+)', 'value': 'asia'}
                    ],
                    value='europe',
                    style={'marginTop': '5px', 'marginBottom': '15px'}
                )
            ]),

            # Cross-Geographic Product Type
            html.Div([
                html.Label("Product Type:", style={'fontWeight': 'bold', 'color': '#2c3e50', 'fontSize': '14px'}),
                dcc.Dropdown(
                    id='cross-geo-product-dropdown',
                    options=[
                        {'label': '🛢️ All Products', 'value': 'all'},
                        {'label': '⚫ Crude Oil', 'value': 'crude_oil'},
                        {'label': '🔵 Total Products', 'value': 'total_products'},
                        {'label': '🟢 Gasoline', 'value': 'gasoline'},
                        {'label': '🟡 Distillates', 'value': 'distillates'}
                    ],
                    value='total_products',
                    style={'marginTop': '5px', 'marginBottom': '15px'}
                )
            ]),

            # Cross-Geographic Storage Type
            html.Div([
                html.Label("Storage Type:", style={'fontWeight': 'bold', 'color': '#2c3e50', 'fontSize': '14px'}),
                dcc.Dropdown(
                    id='cross-geo-storage-dropdown',
                    options=[
                        {'label': '🏭 Land Storage (Regional Data Available)', 'value': 'land'},
                        {'label': '🚢 Oil on Water (Global Data Only)', 'value': 'water', 'disabled': True},
                        {'label': '⚓ Floating Storage (Global Data Only)', 'value': 'floating', 'disabled': True},
                        {'label': '🌍 All Storage Types (Uses Land Data)', 'value': 'all'}
                    ],
                    value='land',
                    style={'marginTop': '5px'}
                )
            ]),

            # Add explanatory note
            html.Div([
                html.P("Note: Cross-geographic analysis requires regional data breakdowns. Water and Floating storage only have global totals.",
                       style={'fontSize': '11px', 'color': '#7f8c8d', 'fontStyle': 'italic', 'marginTop': '5px'})
            ])

        ], style={'width': '25%', 'display': 'inline-block', 'verticalAlign': 'top',
                 'padding': '20px', 'backgroundColor': '#e8f5e8', 'margin': '20px',
                 'borderRadius': '10px', 'marginRight': '0px'}),

        # Right Panel - Cross-Geographic Chart
        html.Div([
            html.Div([
                html.H3("🌍 Cross-Geographic Analysis", style={'color': '#34495e'}),
                html.P("Difference between selected geographic regions | Positive values: Primary region has higher inventory | Negative values: Comparison region has higher inventory",
                       style={'color': '#7f8c8d', 'fontStyle': 'italic', 'marginBottom': '15px'}),
                dcc.Graph(id='cross-geographic-chart', style={'height': '500px'})
            ], style={'padding': '20px', 'backgroundColor': '#ffffff', 'borderRadius': '10px', 'boxShadow': '0 2px 4px rgba(0,0,0,0.1)'})

        ], style={'width': '70%', 'display': 'inline-block', 'verticalAlign': 'top', 'margin': '20px', 'marginLeft': '0px'})

    ], style={'marginBottom': '40px', 'display': 'flex', 'alignItems': 'flex-start'}),

    
    # Footer
    html.Div([
        html.P("Global Liquids Inventory Dashboard | Data updated weekly", 
               style={'textAlign': 'center', 'color': '#95a5a6', 'marginTop': '40px'})
    ])
], style={'fontFamily': 'Arial, sans-serif', 'backgroundColor': '#f8f9fa'})


# Callback to update column dropdown based on other selections
@app.callback(
    Output('column-dropdown', 'options'),
    Output('column-dropdown', 'value'),
    [Input('product-type-dropdown', 'value'),
     Input('geography-dropdown', 'value'),
     Input('storage-type-dropdown', 'value')]
)
def update_column_options(product_type, geography, storage_type):
    """Update available columns based on filter selections."""
    try:
        # Get filtered data to see available columns
        df = processor.get_filtered_data(product_type, geography, storage_type)
        numeric_cols = df.select_dtypes(include=['number']).columns.tolist()
        
        options = [{'label': col, 'value': col} for col in numeric_cols]
        default_value = numeric_cols[0] if numeric_cols else None
        
        return options, default_value
    except Exception as e:
        return [], None


# Callback to update weekly change column dropdown based on other selections
@app.callback(
    Output('weekly-change-column-dropdown', 'options'),
    Output('weekly-change-column-dropdown', 'value'),
    [Input('weekly-change-product-dropdown', 'value'),
     Input('weekly-change-geography-dropdown', 'value'),
     Input('weekly-change-storage-dropdown', 'value')]
)
def update_weekly_change_column_options(product_type, geography, storage_type):
    """Update available columns for weekly change analysis based on filter selections."""
    try:
        # Get filtered data to see available columns
        df = processor.get_filtered_data(product_type, geography, storage_type)
        numeric_cols = df.select_dtypes(include=['number']).columns.tolist()

        options = [{'label': col, 'value': col} for col in numeric_cols]
        default_value = numeric_cols[0] if numeric_cols else None

        return options, default_value
    except Exception as e:
        return [], None


# Weekly Change Chart callback
@app.callback(
    Output('weekly-change-chart', 'figure'),
    [Input('weekly-change-product-dropdown', 'value'),
     Input('weekly-change-geography-dropdown', 'value'),
     Input('weekly-change-storage-dropdown', 'value'),
     Input('weekly-change-years-checklist', 'value'),
     Input('weekly-change-column-dropdown', 'value')]
)
def update_weekly_change_chart(product_type, geography, storage_type, selected_years, column):
    """Update weekly change chart based on user selections."""
    try:
        print(f"📈 Weekly change update: product={product_type}, geo={geography}, storage={storage_type}, years={selected_years}, column={column}")

        # Prepare weekly change chart data
        chart_data = processor.prepare_weekly_change_chart_data(
            product_type=product_type,
            geography=geography,
            storage_type=storage_type,
            selected_years=selected_years,
            main_column=column
        )

        # Create weekly change chart
        weekly_change_chart = create_weekly_change_chart(chart_data)

        return weekly_change_chart

    except Exception as e:
        # Create error chart if something goes wrong
        error_fig = go.Figure()
        error_fig.add_annotation(
            text=f"Error: {str(e)}",
            xref="paper", yref="paper",
            x=0.5, y=0.5, showarrow=False,
            font=dict(size=16, color="red")
        )
        return error_fig


# Correlation ranking callback for main dashboard
@app.callback(
    Output('correlation-ranking', 'children'),
    [Input('product-type-dropdown', 'value'),
     Input('geography-dropdown', 'value'),
     Input('storage-type-dropdown', 'value'),
     Input('column-dropdown', 'value')]
)
def update_correlation_ranking(product_type, geography, storage_type, column):
    """Update 3-month correlation ranking for main dashboard."""
    try:
        if not column:
            return "Select a data column to see correlation analysis"

        # Calculate correlations
        correlation_data = processor.calculate_3month_correlations(
            product_type=product_type,
            geography=geography,
            storage_type=storage_type,
            main_column=column
        )

        if 'error' in correlation_data:
            return f"⚠️ {correlation_data['error']}"

        correlations_df = correlation_data['correlations']
        analysis_window = correlation_data['analysis_window']

        # Create ranking display
        ranking_items = []
        ranking_items.append(html.Div(f"🔍 Analysis: {analysis_window}", style={'fontWeight': 'bold', 'marginBottom': '8px'}))

        for _, row in correlations_df.head(5).iterrows():  # Show top 5
            correlation = row['correlation']
            year = int(row['year'])
            rank = int(row['rank'])
            strength = row['strength']
            direction = row['direction']

            # Color coding based on correlation strength
            if abs(correlation) >= 0.6:
                color = '#27ae60' if correlation > 0 else '#e74c3c'  # Green/Red for strong
            elif abs(correlation) >= 0.4:
                color = '#f39c12'  # Orange for moderate
            else:
                color = '#95a5a6'  # Gray for weak

            ranking_items.append(
                html.Div([
                    html.Span(f"#{rank}", style={'fontWeight': 'bold', 'marginRight': '5px'}),
                    html.Span(f"{year}", style={'fontWeight': 'bold', 'marginRight': '5px'}),
                    html.Span(f"{correlation:.3f}", style={'color': color, 'fontWeight': 'bold', 'marginRight': '5px'}),
                    html.Span(f"({strength})", style={'fontSize': '10px', 'color': '#7f8c8d'})
                ], style={'marginBottom': '3px'})
            )

        if len(correlations_df) > 5:
            ranking_items.append(html.Div(f"... and {len(correlations_df) - 5} more years",
                                        style={'fontSize': '10px', 'color': '#7f8c8d', 'marginTop': '5px'}))

        return ranking_items

    except Exception as e:
        return f"❌ Error: {str(e)}"


# Correlation ranking callback for weekly change dashboard
@app.callback(
    Output('weekly-change-correlation-ranking', 'children'),
    [Input('weekly-change-product-dropdown', 'value'),
     Input('weekly-change-geography-dropdown', 'value'),
     Input('weekly-change-storage-dropdown', 'value'),
     Input('weekly-change-column-dropdown', 'value')]
)
def update_weekly_change_correlation_ranking(product_type, geography, storage_type, column):
    """Update 3-month correlation ranking for weekly change dashboard."""
    try:
        if not column:
            return "Select a data column to see correlation analysis"

        # For weekly change, we need to use the original column (not the _weekly_change column)
        original_column = column.replace('_weekly_change', '') if '_weekly_change' in column else column

        # Calculate correlations
        correlation_data = processor.calculate_3month_correlations(
            product_type=product_type,
            geography=geography,
            storage_type=storage_type,
            main_column=original_column
        )

        if 'error' in correlation_data:
            return f"⚠️ {correlation_data['error']}"

        correlations_df = correlation_data['correlations']
        analysis_window = correlation_data['analysis_window']

        # Create ranking display
        ranking_items = []
        ranking_items.append(html.Div(f"🔍 Analysis: {analysis_window}", style={'fontWeight': 'bold', 'marginBottom': '8px'}))

        for _, row in correlations_df.head(5).iterrows():  # Show top 5
            correlation = row['correlation']
            year = int(row['year'])
            rank = int(row['rank'])
            strength = row['strength']
            direction = row['direction']

            # Color coding based on correlation strength
            if abs(correlation) >= 0.6:
                color = '#27ae60' if correlation > 0 else '#e74c3c'  # Green/Red for strong
            elif abs(correlation) >= 0.4:
                color = '#f39c12'  # Orange for moderate
            else:
                color = '#95a5a6'  # Gray for weak

            ranking_items.append(
                html.Div([
                    html.Span(f"#{rank}", style={'fontWeight': 'bold', 'marginRight': '5px'}),
                    html.Span(f"{year}", style={'fontWeight': 'bold', 'marginRight': '5px'}),
                    html.Span(f"{correlation:.3f}", style={'color': color, 'fontWeight': 'bold', 'marginRight': '5px'}),
                    html.Span(f"({strength})", style={'fontSize': '10px', 'color': '#7f8c8d'})
                ], style={'marginBottom': '3px'})
            )

        if len(correlations_df) > 5:
            ranking_items.append(html.Div(f"... and {len(correlations_df) - 5} more years",
                                        style={'fontSize': '10px', 'color': '#7f8c8d', 'marginTop': '5px'}))

        return ranking_items

    except Exception as e:
        return f"❌ Error: {str(e)}"


# Main callback to update all charts and statistics
@app.callback(
    [Output('main-chart', 'figure'),
     Output('percentile-trend', 'figure'),
     Output('anomaly-flags', 'figure'),
     Output('summary-stats', 'children')],
    [Input('product-type-dropdown', 'value'),
     Input('geography-dropdown', 'value'),
     Input('storage-type-dropdown', 'value'),
     Input('years-checklist', 'value'),
     Input('column-dropdown', 'value')]
)
def update_dashboard(product_type, geography, storage_type, selected_years, column):
    """Update all dashboard components based on user selections."""

    try:
        # Debug print to see what's being called
        print(f"🔄 Dashboard update called: product={product_type}, geo={geography}, storage={storage_type}, years={selected_years}, column={column}")

        # Prepare chart data
        chart_data = processor.prepare_chart_data(
            product_type=product_type,
            geography=geography,
            storage_type=storage_type,
            selected_years=selected_years,
            main_column=column
        )

        # Debug selected years data
        if 'selected_years_data' in chart_data:
            print(f"✅ Selected years data: {chart_data['selected_years_data'].shape}")
        else:
            print("❌ No selected years data found")

        # Create charts
        main_chart = create_main_chart(chart_data)
        percentile_trend = create_percentile_trend_chart(chart_data)
        anomaly_flags = create_anomaly_flags(chart_data)

    except Exception as e:
        # Create error charts if something goes wrong
        error_fig = go.Figure()
        error_fig.add_annotation(
            text=f"Error: {str(e)}",
            xref="paper", yref="paper",
            x=0.5, y=0.5, showarrow=False,
            font=dict(size=16, color="red")
        )
        main_chart = percentile_ribbon = anomaly_flags = error_fig

    # Generate summary statistics
    stats = get_summary_stats(chart_data)

    # Create summary statistics display
    if 'error' in stats:
        summary_div = html.Div([
            html.H4("⚠️ Error", style={'color': '#e74c3c'}),
            html.P(stats['error'])
        ])
    else:
        summary_div = html.Div([
            html.H3("📊 Summary Statistics", style={'color': '#34495e', 'marginBottom': '20px'}),

            html.Div([
                # Current Value Card
                html.Div([
                    html.H4("Current Value", style={'color': '#3498db', 'marginBottom': '10px'}),
                    html.H2(f"{stats.get('current_value', 'N/A'):,.0f}" if isinstance(stats.get('current_value'), (int, float)) else str(stats.get('current_value', 'N/A')),
                            style={'color': '#2c3e50', 'marginBottom': '5px'}),
                    html.P(f"Week: {stats.get('current_week', 'N/A')}", style={'color': '#7f8c8d'})
                ], style={'textAlign': 'center', 'padding': '20px', 'backgroundColor': '#ffffff',
                         'borderRadius': '10px', 'boxShadow': '0 2px 4px rgba(0,0,0,0.1)',
                         'width': '22%', 'display': 'inline-block', 'margin': '1%'}),

                # Trend Card
                html.Div([
                    html.H4("Recent Trend", style={'color': '#27ae60', 'marginBottom': '10px'}),
                    html.H3(stats.get('recent_trend', 'N/A'), style={'color': '#2c3e50', 'marginBottom': '5px'}),
                    html.P(f"Change: {stats.get('trend_value', 0):,.0f}" if isinstance(stats.get('trend_value'), (int, float)) else "N/A",
                           style={'color': '#7f8c8d'})
                ], style={'textAlign': 'center', 'padding': '20px', 'backgroundColor': '#ffffff',
                         'borderRadius': '10px', 'boxShadow': '0 2px 4px rgba(0,0,0,0.1)',
                         'width': '22%', 'display': 'inline-block', 'margin': '1%'}),

                # Data Range Card
                html.Div([
                    html.H4("Data Range", style={'color': '#9b59b6', 'marginBottom': '10px'}),
                    html.P(stats.get('data_range', 'N/A'), style={'color': '#2c3e50', 'fontSize': '14px', 'marginBottom': '5px'}),
                    html.P(f"Total weeks: {stats.get('total_weeks', 'N/A')}", style={'color': '#7f8c8d'})
                ], style={'textAlign': 'center', 'padding': '20px', 'backgroundColor': '#ffffff',
                         'borderRadius': '10px', 'boxShadow': '0 2px 4px rgba(0,0,0,0.1)',
                         'width': '22%', 'display': 'inline-block', 'margin': '1%'}),

                # Anomalies Card
                html.Div([
                    html.H4("Anomalies", style={'color': '#e74c3c', 'marginBottom': '10px'}),
                    html.H3(f"{stats.get('total_anomalies', 0)}", style={'color': '#2c3e50', 'marginBottom': '5px'}),
                    html.P(f"High: {stats.get('high_anomalies', 0)} | Low: {stats.get('low_anomalies', 0)}",
                           style={'color': '#7f8c8d'})
                ], style={'textAlign': 'center', 'padding': '20px', 'backgroundColor': '#ffffff',
                         'borderRadius': '10px', 'boxShadow': '0 2px 4px rgba(0,0,0,0.1)',
                         'width': '22%', 'display': 'inline-block', 'margin': '1%'})
            ])
        ])

    return main_chart, percentile_trend, anomaly_flags, summary_div


# Cross-Geographic Analysis callback
@app.callback(
    Output('cross-geographic-chart', 'figure'),
    [Input('primary-geography-dropdown', 'value'),
     Input('comparison-geography-dropdown', 'value'),
     Input('cross-geo-product-dropdown', 'value'),
     Input('cross-geo-storage-dropdown', 'value'),
     Input('years-checklist', 'value')]
)
def update_cross_geographic_chart(primary_geo, comparison_geo, product_type, storage_type, selected_years):
    """Update cross-geographic analysis chart based on user selections."""

    try:
        print(f"🌍 Cross-geo update: {primary_geo} vs {comparison_geo}, product={product_type}, storage={storage_type}")

        # Prepare cross-geographic chart data
        chart_data = processor.prepare_cross_geographic_chart_data(
            product_type=product_type,
            storage_type=storage_type,
            primary_geography=primary_geo,
            comparison_geography=comparison_geo,
            selected_years=selected_years
        )

        # Create cross-geographic chart
        cross_geo_chart = create_cross_geographic_chart(chart_data)

        return cross_geo_chart

    except Exception as e:
        # Create error chart if something goes wrong
        error_fig = go.Figure()
        error_fig.add_annotation(
            text=f"Error in cross-geographic analysis: {str(e)}",
            xref="paper", yref="paper",
            x=0.5, y=0.5, showarrow=False,
            font=dict(size=16, color="red")
        )
        return error_fig


# Load data when the app starts
if __name__ == '__main__':
    print("🚀 Starting Global Liquids Inventory Dashboard...")
    print("📊 Loading data...")
    processor.load_data()
    print("✅ Data loaded successfully!")
    print("🌐 Starting web server...")
    print("📱 Dashboard will be available at: http://localhost:8050")
    app.run(debug=True, host='0.0.0.0', port=8050)
