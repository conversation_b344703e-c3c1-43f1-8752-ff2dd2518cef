"""
Configuration file for Energy Futures Data Fetcher

This file contains all configurable parameters for the energy futures data pipeline.
Modify these settings to customize data fetching behavior.
"""

from datetime import datetime, timedelta

# =============================================================================
# DATA SOURCE CONFIGURATION
# =============================================================================

# Yahoo Finance ticker mappings
YAHOO_TICKERS = {
    'WTI': 'CL=F',      # WTI Crude Oil Futures (NYMEX)
    'Brent': 'BZ=F',    # Brent Crude Oil Futures (ICE)
    'RBOB': 'RB=F',     # RBOB Gasoline Futures (NYMEX)
    'ULSD': 'HO=F',     # Ultra Low Sulfur Diesel Futures (NYMEX)
}

# Alternative tickers (if primary ones fail)
ALTERNATIVE_TICKERS = {
    'WTI': ['CL=F', 'CLZ24.NYM'],
    'Brent': ['BZ=F', 'BRN=F'],
    'RBOB': ['RB=F', 'XB=F'],
    'ULSD': ['HO=F', 'DF=F'],
}

# =============================================================================
# DATE RANGE CONFIGURATION
# =============================================================================

# Default date range
DEFAULT_START_DATE = '2018-01-01'
DEFAULT_END_DATE = None  # None = today

# Historical data limits
MIN_START_DATE = '2010-01-01'  # Earliest allowed start date
MAX_LOOKBACK_YEARS = 10        # Maximum years of historical data

# =============================================================================
# DATA PROCESSING CONFIGURATION
# =============================================================================

# Weekly resampling settings
WEEKLY_ANCHOR = 'W-FRI'  # Week ending on Friday
FILL_METHOD = 'ffill'    # Forward fill missing values

# Technical indicator settings
MOVING_AVERAGES = {
    'short': 4,   # 4-week (monthly)
    'medium': 13, # 13-week (quarterly)
    'long': 52,   # 52-week (annual)
}

VOLATILITY_WINDOW = 4  # 4-week rolling volatility

# =============================================================================
# SPREAD CALCULATIONS
# =============================================================================

# Crack spread definitions
CRACK_SPREADS = {
    'RBOB_Crack': {'product': 'RBOB', 'crude': 'WTI'},
    'ULSD_Crack': {'product': 'ULSD', 'crude': 'WTI'},
    'Brent_WTI_Spread': {'product': 'Brent', 'crude': 'WTI'},
}

# 3:2:1 crack spread weights (3 barrels crude -> 2 barrels gasoline + 1 barrel distillate)
CRACK_3_2_1_WEIGHTS = {
    'RBOB': 2/3,
    'ULSD': 1/3,
    'WTI': -1,
}

# =============================================================================
# OUTPUT CONFIGURATION
# =============================================================================

# File naming
OUTPUT_FILENAME = 'energy_futures_weekly.csv'
BACKUP_FILENAME = 'energy_futures_weekly_backup.csv'

# Data folder
DATA_FOLDER = 'data'

# CSV export settings
CSV_SETTINGS = {
    'index': True,
    'float_format': '%.4f',
    'date_format': '%Y-%m-%d',
}

# =============================================================================
# DATA QUALITY SETTINGS
# =============================================================================

# Minimum data requirements
MIN_WEEKS_REQUIRED = 52  # Minimum 1 year of data
MAX_MISSING_PCT = 0.1    # Maximum 10% missing data allowed

# Price validation ranges ($/barrel or $/gallon)
PRICE_RANGES = {
    'WTI': {'min': 10, 'max': 200},
    'Brent': {'min': 10, 'max': 200},
    'RBOB': {'min': 0.5, 'max': 10},
    'ULSD': {'min': 0.5, 'max': 10},
}

# Spread validation ranges
SPREAD_RANGES = {
    'RBOB_Crack': {'min': -2, 'max': 5},
    'ULSD_Crack': {'min': -2, 'max': 5},
    'Brent_WTI_Spread': {'min': -20, 'max': 20},
    'Crack_3_2_1': {'min': -2, 'max': 5},
}

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

LOG_LEVEL = 'INFO'  # DEBUG, INFO, WARNING, ERROR
LOG_FORMAT = '%(asctime)s - %(levelname)s - %(message)s'

# =============================================================================
# API SETTINGS
# =============================================================================

# Yahoo Finance download settings
YFINANCE_SETTINGS = {
    'interval': '1d',
    'group_by': 'ticker',
    'auto_adjust': True,
    'prepost': True,
    'threads': True,
    'timeout': 30,
}

# Retry settings
MAX_RETRIES = 3
RETRY_DELAY = 5  # seconds

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable/disable features
ENABLE_TECHNICAL_INDICATORS = True
ENABLE_SPREAD_CALCULATIONS = True
ENABLE_DATA_VALIDATION = True
ENABLE_BACKUP_FILES = True

# Advanced features
CALCULATE_CORRELATIONS = False
CALCULATE_SEASONALITY = False
EXPORT_SUMMARY_STATS = True
