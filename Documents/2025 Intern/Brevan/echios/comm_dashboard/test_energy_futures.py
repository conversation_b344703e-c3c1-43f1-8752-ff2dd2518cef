#!/usr/bin/env python3
"""
Test Energy Futures Data Integration

This script tests the energy futures data and shows how to integrate it with the dashboard.
"""

import pandas as pd
import numpy as np
from datetime import datetime

def test_energy_futures_data():
    """Test the generated energy futures data."""
    
    print("🧪 Testing Energy Futures Data Integration")
    print("="*60)
    
    # Load the data
    try:
        df = pd.read_csv('data/energy_futures_weekly.csv', index_col=0, parse_dates=True)
        print(f"✅ Successfully loaded {len(df)} weeks of data")
        print(f"📅 Date range: {df.index.min().strftime('%Y-%m-%d')} to {df.index.max().strftime('%Y-%m-%d')}")
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return
    
    # Check data structure
    print(f"\n📊 Data Structure:")
    print(f"   Columns: {len(df.columns)}")
    print(f"   Price columns: {[col for col in df.columns if col in ['WTI', 'Brent', 'RBOB', 'ULSD']]}")
    print(f"   Spread columns: {[col for col in df.columns if 'Crack' in col or 'Spread' in col]}")
    
    # Show latest prices
    latest = df.iloc[-1]
    print(f"\n💰 Latest Prices (as of {df.index[-1].strftime('%Y-%m-%d')}):")
    print(f"   WTI Crude:     ${latest['WTI']:.2f}/barrel")
    print(f"   Brent Crude:   ${latest['Brent']:.2f}/barrel")
    print(f"   RBOB Gasoline: ${latest['RBOB']:.3f}/gallon")
    print(f"   ULSD Diesel:   ${latest['ULSD']:.3f}/gallon")
    
    # Show latest spreads
    print(f"\n🔧 Latest Spreads:")
    if 'RBOB_Crack' in df.columns:
        print(f"   RBOB Crack:    ${latest['RBOB_Crack']:.3f}/gallon")
    if 'ULSD_Crack' in df.columns:
        print(f"   ULSD Crack:    ${latest['ULSD_Crack']:.3f}/gallon")
    if 'Brent_WTI_Spread' in df.columns:
        print(f"   Brent-WTI:     ${latest['Brent_WTI_Spread']:.2f}/barrel")
    
    # Calculate some statistics
    print(f"\n📈 2025 YTD Statistics:")
    df_2025 = df[df.index.year == 2025]
    if not df_2025.empty:
        print(f"   WTI Range:     ${df_2025['WTI'].min():.2f} - ${df_2025['WTI'].max():.2f}")
        print(f"   WTI Volatility: {df_2025['WTI'].std():.2f}")
        print(f"   Weeks of data: {len(df_2025)}")
    
    # Test correlation analysis (similar to dashboard)
    print(f"\n🔍 Sample Correlation Analysis:")
    if len(df) > 52:  # Need at least 1 year of data
        # Calculate 3-month correlation between 2025 and 2024
        current_week = 24  # Simulate current week
        start_week = max(1, current_week - 13 + 1)
        end_week = current_week
        
        df['week_of_year'] = df.index.isocalendar().week
        
        # Get 2025 data
        df_2025_window = df[(df.index.year == 2025) & 
                           (df['week_of_year'] >= start_week) & 
                           (df['week_of_year'] <= end_week)]
        
        # Get 2024 data for same weeks
        df_2024_window = df[(df.index.year == 2024) & 
                           (df['week_of_year'] >= start_week) & 
                           (df['week_of_year'] <= end_week)]
        
        if len(df_2025_window) > 3 and len(df_2024_window) > 3:
            # Align by week of year
            wti_2025 = df_2025_window.set_index('week_of_year')['WTI']
            wti_2024 = df_2024_window.set_index('week_of_year')['WTI']
            
            common_weeks = wti_2025.index.intersection(wti_2024.index)
            if len(common_weeks) > 3:
                correlation = wti_2025.loc[common_weeks].corr(wti_2024.loc[common_weeks])
                print(f"   2025 vs 2024 WTI correlation (weeks {start_week}-{end_week}): {correlation:.3f}")
                
                if correlation > 0.6:
                    print(f"   📊 Strong positive correlation - 2025 following 2024 patterns")
                elif correlation < -0.6:
                    print(f"   📊 Strong negative correlation - 2025 opposite to 2024 patterns")
                else:
                    print(f"   📊 Moderate correlation - mixed pattern relationship")
    
    # Test weekly changes calculation
    print(f"\n📊 Weekly Changes Analysis:")
    df['WTI_weekly_change'] = df['WTI'].diff()
    recent_changes = df['WTI_weekly_change'].tail(5).dropna()
    
    if not recent_changes.empty:
        print(f"   Recent WTI weekly changes:")
        for date, change in recent_changes.items():
            direction = "📈" if change > 0 else "📉"
            print(f"     {date.strftime('%Y-%m-%d')}: {direction} ${change:+.2f}")
    
    # Integration suggestions
    print(f"\n🔗 Dashboard Integration Suggestions:")
    print(f"   1. Add energy futures as new data source in inventory_dashboard_functions.py")
    print(f"   2. Create new chart type for futures prices vs inventory levels")
    print(f"   3. Add correlation analysis between crude prices and inventory changes")
    print(f"   4. Include crack spreads as market indicators")
    print(f"   5. Use futures volatility as risk indicator")
    
    print(f"\n✅ Energy futures data is ready for dashboard integration!")
    print("="*60)

def show_integration_example():
    """Show example of how to integrate with existing dashboard."""
    
    print("\n🔧 Integration Example:")
    print("="*40)
    
    code_example = '''
# Example: Add to inventory_dashboard_functions.py

def load_energy_futures_data(self):
    """Load energy futures data."""
    futures_path = os.path.join(self.data_path, 'energy_futures_weekly.csv')
    if os.path.exists(futures_path):
        self.futures_data = pd.read_csv(futures_path, index_col=0, parse_dates=True)
        return True
    return False

def create_futures_inventory_correlation_chart(self, product_type='crude_oil'):
    """Create chart showing correlation between futures prices and inventory levels."""
    
    # Get inventory data
    inventory_data = self.get_filtered_data(product_type, 'all', 'all')
    
    # Get futures data
    if hasattr(self, 'futures_data'):
        futures_col = 'WTI' if product_type == 'crude_oil' else 'Brent'
        
        # Merge data by date
        merged = pd.merge(inventory_data, self.futures_data[futures_col], 
                         left_index=True, right_index=True, how='inner')
        
        # Calculate correlation
        correlation = merged.iloc[:, 0].corr(merged[futures_col])
        
        # Create scatter plot with trend line
        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=merged[futures_col],
            y=merged.iloc[:, 0],
            mode='markers',
            name=f'{product_type.title()} Inventory vs {futures_col} Price',
            text=[f"Week: {date.strftime('%Y-%m-%d')}" for date in merged.index],
            hovertemplate='Price: $%{x:.2f}<br>Inventory: %{y:,.0f}<br>%{text}<extra></extra>'
        ))
        
        fig.update_layout(
            title=f'📊 {product_type.title()} Inventory vs {futures_col} Price (Correlation: {correlation:.3f})',
            xaxis_title=f'{futures_col} Price ($/barrel)',
            yaxis_title='Inventory Level (barrels)',
            template='plotly_white'
        )
        
        return fig
    '''
    
    print(code_example)

if __name__ == "__main__":
    test_energy_futures_data()
    show_integration_example()
