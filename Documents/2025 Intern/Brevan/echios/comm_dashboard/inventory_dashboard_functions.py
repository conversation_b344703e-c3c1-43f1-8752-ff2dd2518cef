"""
Global Liquids Inventory Data Tracking Dashboard Functions

This module contains all the core functions for processing and analyzing
global liquids inventory data for the dynamic dashboard.

Features:
- Data loading and preprocessing
- 5-year statistical analysis
- Week-of-year percentile calculations
- Anomaly detection
- Drill-down filtering capabilities
"""

import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Union
import warnings
warnings.filterwarnings('ignore')


class InventoryDataProcessor:
    """Main class for processing global liquids inventory data."""
    
    def __init__(self, data_path: str = "data/"):
        """
        Initialize the data processor.
        
        Args:
            data_path (str): Path to the data directory
        """
        self.data_path = data_path
        self.data = {}
        self.product_mapping = self._create_product_mapping()
        self.geography_mapping = self._create_geography_mapping()
        self.storage_mapping = self._create_storage_mapping()
        
    def _create_product_mapping(self) -> Dict[str, List[str]]:
        """Create mapping of product types to column names based on actual data."""
        return {
            'crude_oil': [
                # Crude oil specific columns
                'Total Crude Land + Water', 'Total Global Commercial Crude Oil',
                'US Commercial Crude (EIA)', 'US SPR Crude (EIA)', 'Crude/Co Oil on Water',
                'Crude/Co Floating Storage'
            ],
            'total_products': [
                # Total products columns
                'Total Products Land + Water', 'Total Global Products Land',
                'US Products (EIA)', 'ARA Total Products (PJK)',
                'Sing Total Products (IE)', 'Total Products Oil on Water',
                'Total Products Floating Storage'
            ],
            'gasoline': [
                # Gasoline specific columns
                'Total Global Gasoline Land + Water', 'Total Global Gasoline Land',
                'US Gasoline (EIA)', 'ARA Gasoline (PJK)', 'Gasoline Oil on Water',
                'Gasoline Floating Storage', 'Sing Light Ends (IE)'
            ],
            'distillates': [
                # Distillate specific columns
                'Total Global Distillate Land + Water', 'Total Global Distillate Land',
                'US Distillate (EIA)', 'ARA Distillate (PJK)', 'Sing Middle Distillates (IE)',
                'Distillate Oil on Water', 'Distillate Floating Storage', 'ARA Jet (PJK)'
            ],
            'core_products': [
                # Core products (crude + key products)
                'Total Crude / Core Products Land + Water', 'Total Crude / Core Products on Land',
                'Total Global Core Products Land + Water', 'Total Global Core Products Land',
                'Core Products on Water', 'Core Products Floating'
            ]
        }
    
    def _create_geography_mapping(self) -> Dict[str, List[str]]:
        """Create mapping of geographic regions to column names based on actual data."""
        return {
            'global': [
                # Total/Global columns (from merge_total.xlsx and others)
                'Total Land + Water', 'Total Crude Land + Water', 'Total Products Land + Water',
                'Total Crude / Core Products Land + Water', 'Total Global Commercial Liquids on Land',
                'Total Crude / Core Products on Land', 'Total Global Commercial Crude Oil',
                'Total Global Products Land', 'Total Global Core Products Land + Water',
                'Total Global Gasoline Land + Water', 'Total Global Distillate Land + Water',
                'Total Global Core Products Land', 'Total Global Gasoline Land', 'Total Global Distillate Land',
                'Total Oil on Water', 'Total Floating Storage', 'Total Products Oil on Water',
                'Total Products Floating Storage'
            ],
            'us': [
                # US-specific columns (from merge_land.xlsx)
                'US Commercial Crude (EIA)', 'US SPR Crude (EIA)', 'US Products (EIA)',
                'US Gasoline (EIA)', 'US Distillate (EIA)', 'US Resid (EIA)'
            ],
            'europe': [
                # ARA (Amsterdam-Rotterdam-Antwerp) and Europe columns
                'ARA Total Products (PJK)', 'ARA Gasoline (PJK)', 'ARA Distillate (PJK)',
                'ARA Jet (PJK)', 'ARA Resid (PJK)', 'Kpler Land Europe'
            ],
            'asia': [
                # Singapore and Asia columns
                'Sing Total Products (IE)', 'Sing Light Ends (IE)', 'Sing Middle Distillates (IE)',
                'Kpler Land China', 'Kpler Land SE Asia'
            ]
        }
    
    def _create_storage_mapping(self) -> Dict[str, str]:
        """Create mapping of storage types to file names."""
        return {
            'land': 'merge_land.xlsx',
            'water': 'merge_water.xlsx', 
            'floating': 'merge_floating.xlsx',
            'total': 'merge_total.xlsx'
        }
    
    def load_data(self) -> None:
        """Load all data files into memory."""
        print("Loading inventory data...")
        
        for storage_type, filename in self.storage_mapping.items():
            try:
                filepath = f"{self.data_path}{filename}"
                df = pd.read_excel(filepath)
                
                # Ensure Week column is datetime
                df['Week'] = pd.to_datetime(df['Week'])
                
                # Sort by date
                df = df.sort_values('Week').reset_index(drop=True)
                
                self.data[storage_type] = df
                print(f"Loaded {storage_type}: {df.shape}")
                
            except Exception as e:
                print(f"✗ Error loading {filename}: {e}")
        
        print(f"Data loading complete. Available storage types: {list(self.data.keys())}")
    
    def get_filtered_data(self,
                         product_type: str = 'all',
                         geography: str = 'all',
                         storage_type: str = 'all') -> pd.DataFrame:
        """
        Get filtered data based on drill-down selections.

        Args:
            product_type (str): Product type filter
            geography (str): Geography filter
            storage_type (str): Storage type filter

        Returns:
            pd.DataFrame: Filtered data
        """
        try:
            # Select base dataset based on storage type
            if storage_type == 'all':
                df = self.data['total'].copy()
            else:
                df = self.data.get(storage_type, self.data['total']).copy()

            # Get all available columns except Week
            all_cols = [col for col in df.columns if col != 'Week']
            selected_cols = []

            # Simple keyword-based filtering
            for col in all_cols:
                include_col = True

                # Geography filtering
                if geography == 'us' and 'US' not in col:
                    include_col = False
                elif geography == 'europe' and not any(x in col for x in ['ARA', 'Europe']):
                    include_col = False
                elif geography == 'asia' and not any(x in col for x in ['Sing', 'China', 'SE Asia']):
                    include_col = False
                elif geography == 'global' and not any(x in col for x in ['Total', 'Global']):
                    include_col = False

                # Product filtering
                if product_type == 'crude_oil' and not any(x in col for x in ['Crude', 'crude']):
                    include_col = False
                elif product_type == 'gasoline' and not any(x in col for x in ['Gasoline', 'gasoline', 'Light Ends']):
                    include_col = False
                elif product_type == 'distillates' and not any(x in col for x in ['Distillate', 'distillate', 'Middle Distillates', 'Jet']):
                    include_col = False
                elif product_type == 'total_products' and not any(x in col for x in ['Products', 'products']) or 'Crude' in col:
                    include_col = False
                elif product_type == 'core_products' and not any(x in col for x in ['Core', 'core']):
                    include_col = False

                if include_col:
                    selected_cols.append(col)

            # If no columns selected, use all columns
            if not selected_cols:
                selected_cols = all_cols

            # Return filtered dataframe
            return df[['Week'] + selected_cols]

        except Exception as e:
            print(f"Error in filtering: {e}")
            # Return original data if error
            return self.data['total'].copy()

    def calculate_5year_stats(self,
                             df: pd.DataFrame,
                             column: str,
                             selected_years: List[int] = None) -> pd.DataFrame:
        """
        Calculate 5-year statistics for future projection (always for 2025+).
        This creates the shading bands for future projection.

        Args:
            df (pd.DataFrame): Input data
            column (str): Column to analyze
            selected_years (List[int]): Not used for shading - shading is always based on historical data

        Returns:
            pd.DataFrame: Statistics by week of year for future projection
        """
        if column not in df.columns:
            return pd.DataFrame()

        # Add week of year
        df_work = df.copy()
        df_work['week_of_year'] = df_work['Week'].dt.isocalendar().week
        df_work['year'] = df_work['Week'].dt.year

        # For 5-year shading, always use the most recent 5 years of historical data
        # This creates the projection bands for 2025 and beyond
        current_year = datetime.now().year
        historical_years = [current_year - i for i in range(1, 6)]  # Last 5 years before current

        # Use historical data to create projection bands
        df_historical = df_work[df_work['year'].isin(historical_years)]

        # If not enough historical data, use all available data
        if df_historical.empty or len(df_historical['year'].unique()) < 3:
            df_historical = df_work

        # Calculate statistics by week of year for projection
        stats = df_historical.groupby('week_of_year')[column].agg([
            'min', 'max', 'mean', 'std', 'count'
        ]).reset_index()

        stats.columns = ['week_of_year', 'min_5yr', 'max_5yr', 'avg_5yr', 'std_5yr', 'count']

        return stats

    def get_selected_years_data(self,
                               df: pd.DataFrame,
                               column: str,
                               selected_years: List[int] = None) -> pd.DataFrame:
        """
        Get data for selected years to show as overlay lines.

        Args:
            df (pd.DataFrame): Input data
            column (str): Column to analyze
            selected_years (List[int]): Years to show as overlay lines

        Returns:
            pd.DataFrame: Data for selected years
        """
        if column not in df.columns or not selected_years:
            print(f"get_selected_years_data: Early return - column in df: {column in df.columns if not df.empty else 'df empty'}, selected_years: {selected_years}")
            return pd.DataFrame()

        df_work = df.copy()
        df_work['week_of_year'] = df_work['Week'].dt.isocalendar().week
        df_work['year'] = df_work['Week'].dt.year

        print(f"get_selected_years_data: Input data shape: {df_work.shape}")
        print(f"get_selected_years_data: Available years in input: {sorted(df_work['year'].unique())}")
        print(f"get_selected_years_data: Requested years: {selected_years}")

        # Filter for selected years
        df_selected = df_work[df_work['year'].isin(selected_years)]

        print(f"get_selected_years_data: Output data shape: {df_selected.shape}")
        print(f"get_selected_years_data: Years found in output: {sorted(df_selected['year'].unique()) if not df_selected.empty else 'None'}")

        return df_selected

    def calculate_percentiles(self,
                             df: pd.DataFrame,
                             column: str) -> pd.DataFrame:
        """
        Calculate percentiles for week-of-year ribbon visualization.
        This is the old function - kept for compatibility.

        Args:
            df (pd.DataFrame): Input data
            column (str): Column to analyze

        Returns:
            pd.DataFrame: Data with percentile rankings
        """
        if column not in df.columns:
            return pd.DataFrame()

        df_work = df.copy()
        df_work['week_of_year'] = df_work['Week'].dt.isocalendar().week

        # Calculate percentiles for each week of year
        def calc_percentile(group):
            values = group[column].dropna()
            if len(values) == 0:
                return group

            # Calculate percentile for each value within its week-of-year group
            group['percentile'] = group[column].rank(pct=True) * 100
            return group

        result = df_work.groupby('week_of_year').apply(calc_percentile)
        result = result.reset_index(drop=True)

        return result

    def calculate_2025_percentiles(self,
                                  df: pd.DataFrame,
                                  column: str) -> pd.DataFrame:
        """
        Calculate percentiles for 2025 data against 5-year historical distribution.
        This is specifically for the Week-of-Year Percentile Ribbon.

        Args:
            df (pd.DataFrame): Input data (all years) - should be filtered by drill-down options
            column (str): Column to analyze

        Returns:
            pd.DataFrame: 2025 data with percentile rankings against historical distribution
        """
        print(f"calculate_2025_percentiles: Input data shape: {df.shape}")
        print(f"calculate_2025_percentiles: Column: {column}")
        print(f"calculate_2025_percentiles: Available columns: {list(df.columns)}")

        if column not in df.columns:
            print(f"calculate_2025_percentiles: Column {column} not found in data")
            return pd.DataFrame()

        df_work = df.copy()
        df_work['week_of_year'] = df_work['Week'].dt.isocalendar().week
        df_work['year'] = df_work['Week'].dt.year

        print(f"calculate_2025_percentiles: Available years: {sorted(df_work['year'].unique())}")

        # Get 2025 data
        df_2025 = df_work[df_work['year'] == 2025].copy()

        print(f"calculate_2025_percentiles: 2025 data shape: {df_2025.shape}")

        if df_2025.empty:
            print(f"calculate_2025_percentiles: No 2025 data found")
            return pd.DataFrame()

        # Calculate percentiles for each 2025 week against historical distribution
        percentile_results = []

        for i, row_2025 in df_2025.iterrows():
            week_of_year = row_2025['week_of_year']
            value_2025 = row_2025[column]

            if pd.isna(value_2025):
                print(f"Week {week_of_year}: Skipping - 2025 value is NaN")
                continue

            # Get all historical data for this week of year (all years)
            historical_week_data = df_work[df_work['week_of_year'] == week_of_year]
            historical_values = historical_week_data[column].dropna()

            if len(historical_values) > 0:
                # Calculate percentile: what percentage of historical values are below the 2025 value
                percentile = (historical_values < value_2025).sum() / len(historical_values) * 100

                # Debug output for first few weeks
                if week_of_year <= 3:
                    print(f"Week {week_of_year}: 2025 value = {value_2025:.1f}")
                    print(f"Week {week_of_year}: Historical values count = {len(historical_values)}")
                    print(f"Week {week_of_year}: Historical range = {historical_values.min():.1f} to {historical_values.max():.1f}")
                    print(f"Week {week_of_year}: Values below 2025 = {(historical_values < value_2025).sum()}")
                    print(f"Week {week_of_year}: Calculated percentile = {percentile:.1f}%")

                # Create result row
                result_row = row_2025.copy()
                result_row['percentile'] = percentile
                percentile_results.append(result_row)
            else:
                print(f"Week {week_of_year}: No historical data found")

        print(f"calculate_2025_percentiles: Generated {len(percentile_results)} percentile results")

        if percentile_results:
            result_df = pd.DataFrame(percentile_results)
            return result_df.reset_index(drop=True)
        else:
            return pd.DataFrame()

    def detect_anomalies(self,
                        df: pd.DataFrame,
                        column: str,
                        threshold: float = 0.1) -> pd.DataFrame:
        """
        Detect anomalies (top/bottom percentile values).

        Args:
            df (pd.DataFrame): Input data with percentiles
            column (str): Column to analyze
            threshold (float): Threshold for anomaly detection (0.1 = 10%)

        Returns:
            pd.DataFrame: Data with anomaly flags
        """
        if 'percentile' not in df.columns:
            df = self.calculate_percentiles(df, column)

        df_result = df.copy()

        # Flag anomalies
        df_result['is_anomaly_high'] = df_result['percentile'] >= (100 - threshold * 100)
        df_result['is_anomaly_low'] = df_result['percentile'] <= (threshold * 100)
        df_result['is_anomaly'] = df_result['is_anomaly_high'] | df_result['is_anomaly_low']

        return df_result

    def get_latest_week_data(self,
                            df: pd.DataFrame,
                            column: str) -> Dict:
        """
        Get data for the most recent week.

        Args:
            df (pd.DataFrame): Input data
            column (str): Column to analyze

        Returns:
            Dict: Latest week information
        """
        if df.empty or column not in df.columns:
            return {}

        latest_row = df.loc[df['Week'].idxmax()]

        return {
            'week': latest_row['Week'],
            'value': latest_row[column],
            'week_of_year': latest_row['Week'].isocalendar().week if pd.notna(latest_row['Week']) else None
        }

    def get_cross_geographic_data(self,
                                 product_type: str = 'all',
                                 storage_type: str = 'all',
                                 primary_geography: str = 'us',
                                 comparison_geography: str = 'europe') -> pd.DataFrame:
        """
        Get data for cross-geographic analysis (difference between two regions).

        Args:
            product_type (str): Product type filter
            storage_type (str): Storage type filter
            primary_geography (str): Primary geography (e.g., 'us')
            comparison_geography (str): Comparison geography (e.g., 'europe')

        Returns:
            pd.DataFrame: Data with cross-geographic differences
        """
        try:
            # Select appropriate data source based on storage type
            if storage_type == 'all':
                # For 'all', use land data which has the most geographic breakdowns
                base_data = self.data.get('land', pd.DataFrame())
            else:
                # Use specific storage type data
                base_data = self.data.get(storage_type, pd.DataFrame())

            if base_data.empty:
                return pd.DataFrame()

            # Get region-specific columns based on geography and storage type
            primary_columns = self._get_geographic_columns(primary_geography, product_type, storage_type)
            comparison_columns = self._get_geographic_columns(comparison_geography, product_type, storage_type)

            if not primary_columns or not comparison_columns:
                # Create an informative error message for unsupported storage types
                if storage_type in ['water', 'floating']:
                    error_df = pd.DataFrame({
                        'Week': [pd.Timestamp('2025-01-01')],
                        'error_message': [f'{storage_type.title()} storage data does not have regional breakdowns. Cross-geographic analysis is only available for Land storage.'],
                        'storage_type': [storage_type]
                    })
                    return error_df
                return pd.DataFrame()

            # Select the best matching column for each geography
            primary_col = primary_columns[0]  # Take first available column
            comparison_col = comparison_columns[0]  # Take first available column

            # Ensure both columns exist in the data
            if primary_col not in base_data.columns or comparison_col not in base_data.columns:
                return pd.DataFrame()

            # Create the difference data
            result_data = base_data[['Week', primary_col, comparison_col]].copy()

            # Calculate difference (Primary - Comparison)
            difference_column = f"{primary_geography.upper()} - {comparison_geography.upper()}"
            result_data[difference_column] = result_data[primary_col] - result_data[comparison_col]

            # Add metadata columns
            result_data['primary_geography'] = primary_geography
            result_data['comparison_geography'] = comparison_geography
            result_data['primary_column'] = primary_col
            result_data['comparison_column'] = comparison_col

            return result_data[['Week', difference_column, 'primary_geography', 'comparison_geography',
                              'primary_column', 'comparison_column']]

        except Exception as e:
            print(f"Error in cross-geographic analysis: {e}")
            return pd.DataFrame()

    def _get_geographic_columns(self, geography: str, product_type: str, storage_type: str = 'land') -> List[str]:
        """
        Get appropriate columns for a specific geography, product type, and storage type.

        Args:
            geography (str): Geographic region ('us', 'europe', 'asia')
            product_type (str): Product type filter
            storage_type (str): Storage type ('land', 'water', 'floating', 'all')

        Returns:
            List[str]: List of appropriate column names
        """
        # Define geographic column patterns by storage type
        if storage_type == 'land' or storage_type == 'all':
            geographic_patterns = {
                'us': ['US Commercial Crude (EIA)', 'US SPR Crude (EIA)', 'US Products (EIA)',
                       'US Gasoline (EIA)', 'US Distillate (EIA)', 'US Resid (EIA)'],
                'europe': ['ARA Total Products (PJK)', 'ARA Gasoline (PJK)', 'ARA Distillate (PJK)',
                          'ARA Jet (PJK)', 'ARA Resid (PJK)', 'Kpler Land Europe'],
                'asia': ['Sing Total Products (IE)', 'Sing Light Ends (IE)', 'Sing Middle Distillates (IE)',
                        'Kpler Land China', 'Kpler Land SE Asia']
            }
        elif storage_type == 'water':
            # For water/maritime data, regional breakdown is not available
            # Return empty list to indicate no cross-geographic comparison possible
            return []
        elif storage_type == 'floating':
            # For floating storage, regional breakdown is not available
            # Return empty list to indicate no cross-geographic comparison possible
            return []
        else:
            # Fallback to land patterns
            geographic_patterns = {
                'us': ['US Commercial Crude (EIA)', 'US SPR Crude (EIA)', 'US Products (EIA)',
                       'US Gasoline (EIA)', 'US Distillate (EIA)', 'US Resid (EIA)'],
                'europe': ['ARA Total Products (PJK)', 'ARA Gasoline (PJK)', 'ARA Distillate (PJK)',
                          'ARA Jet (PJK)', 'ARA Resid (PJK)', 'Kpler Land Europe'],
                'asia': ['Sing Total Products (IE)', 'Sing Light Ends (IE)', 'Sing Middle Distillates (IE)',
                        'Kpler Land China', 'Kpler Land SE Asia']
            }

        # Get base columns for the geography
        base_columns = geographic_patterns.get(geography, [])

        # Filter by product type
        if product_type == 'crude_oil':
            filtered_cols = [col for col in base_columns if any(x in col for x in ['Crude', 'crude'])]
        elif product_type == 'total_products':
            filtered_cols = [col for col in base_columns if any(x in col for x in ['Products', 'products']) and 'Crude' not in col]
        elif product_type == 'gasoline':
            filtered_cols = [col for col in base_columns if any(x in col for x in ['Gasoline', 'gasoline', 'Light Ends'])]
        elif product_type == 'distillates':
            filtered_cols = [col for col in base_columns if any(x in col for x in ['Distillate', 'distillate', 'Middle Distillates', 'Jet'])]
        else:
            # For 'all', prefer total products, then crude, then any available
            filtered_cols = [col for col in base_columns if 'Products' in col]
            if not filtered_cols:
                filtered_cols = [col for col in base_columns if 'Crude' in col]
            if not filtered_cols:
                filtered_cols = base_columns

        return filtered_cols if filtered_cols else base_columns

    def _match_geographic_columns(self, primary_cols: List[str], comparison_cols: List[str],
                                 product_type: str) -> Dict[str, str]:
        """
        Match columns between different geographic regions for the same product type.

        Args:
            primary_cols (List[str]): Columns from primary geography
            comparison_cols (List[str]): Columns from comparison geography
            product_type (str): Product type to match

        Returns:
            Dict[str, str]: Matched column names {'primary': col1, 'comparison': col2}
        """
        # Define product keywords for matching
        product_keywords = {
            'crude_oil': ['Crude', 'crude'],
            'total_products': ['Products', 'products'],
            'gasoline': ['Gasoline', 'gasoline', 'Light Ends'],
            'distillates': ['Distillate', 'distillate', 'Middle Distillates'],
            'all': ['Total', 'total']
        }

        keywords = product_keywords.get(product_type, product_keywords['all'])

        # Find best matching columns
        primary_match = None
        comparison_match = None

        # Look for columns containing the product keywords
        for keyword in keywords:
            if not primary_match:
                primary_match = next((col for col in primary_cols if keyword in col), None)
            if not comparison_match:
                comparison_match = next((col for col in comparison_cols if keyword in col), None)

            if primary_match and comparison_match:
                break

        # Fallback: use first available column if no keyword match
        if not primary_match and primary_cols:
            primary_match = primary_cols[0]
        if not comparison_match and comparison_cols:
            comparison_match = comparison_cols[0]

        if primary_match and comparison_match:
            return {'primary': primary_match, 'comparison': comparison_match}
        else:
            return {}

    def prepare_chart_data(self,
                          product_type: str = 'all',
                          geography: str = 'all',
                          storage_type: str = 'all',
                          selected_years: List[int] = None,
                          main_column: str = None) -> Dict:
        """
        Prepare all data needed for chart visualization.

        Args:
            product_type (str): Product type filter
            geography (str): Geography filter
            storage_type (str): Storage type filter
            selected_years (List[int]): Years to include
            main_column (str): Main column to analyze

        Returns:
            Dict: Complete chart data package
        """
        # Get filtered data
        df = self.get_filtered_data(product_type, geography, storage_type)

        if df.empty:
            return {'error': 'No data available for selected filters'}

        # Auto-select main column if not provided
        if main_column is None:
            numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
            main_column = numeric_cols[0] if numeric_cols else None

        if main_column is None or main_column not in df.columns:
            return {'error': 'No suitable column found for analysis'}

        # Calculate all required statistics
        stats_5yr = self.calculate_5year_stats(df, main_column, selected_years)  # For future projection shading

        # For selected years overlay, we need to get data from the same filtered dataset
        # but ensure we get all requested years, not just what's in the current filtered data
        selected_years_data = self.get_selected_years_data(df, main_column, selected_years)  # For overlay lines

        df_with_percentiles = self.calculate_2025_percentiles(df, main_column)  # For 2025 percentile ribbon
        df_with_anomalies = self.detect_anomalies(df_with_percentiles, main_column)
        latest_data = self.get_latest_week_data(df, main_column)

        return {
            'main_data': df,
            'column': main_column,
            'stats_5yr': stats_5yr,
            'selected_years_data': selected_years_data,
            'selected_years': selected_years,
            'percentile_data': df_with_percentiles,
            'anomaly_data': df_with_anomalies,
            'latest_week': latest_data,
            'available_columns': df.select_dtypes(include=[np.number]).columns.tolist()
        }

    def prepare_cross_geographic_chart_data(self,
                                          product_type: str = 'all',
                                          storage_type: str = 'all',
                                          primary_geography: str = 'us',
                                          comparison_geography: str = 'europe',
                                          selected_years: List[int] = None) -> Dict:
        """
        Prepare all data needed for cross-geographic chart visualization.

        Args:
            product_type (str): Product type filter
            storage_type (str): Storage type filter
            primary_geography (str): Primary geography
            comparison_geography (str): Comparison geography
            selected_years (List[int]): Years to include

        Returns:
            Dict: Complete cross-geographic chart data package
        """
        # Get cross-geographic difference data
        df = self.get_cross_geographic_data(product_type, storage_type,
                                          primary_geography, comparison_geography)

        if df.empty:
            return {'error': f'No data available for {primary_geography} vs {comparison_geography} comparison'}

        # Check if this is an error case (unsupported storage type)
        if 'error_message' in df.columns:
            return {'error': df['error_message'].iloc[0]}

        # The difference column name
        difference_column = f"{primary_geography.upper()} - {comparison_geography.upper()}"

        if difference_column not in df.columns:
            return {'error': 'Could not calculate geographic difference'}

        # Calculate all required statistics for the difference data
        stats_5yr = self.calculate_5year_stats(df, difference_column, selected_years)
        selected_years_data = self.get_selected_years_data(df, difference_column, selected_years)
        df_with_percentiles = self.calculate_2025_percentiles(df, difference_column)
        df_with_anomalies = self.detect_anomalies(df_with_percentiles, difference_column)
        latest_data = self.get_latest_week_data(df, difference_column)

        return {
            'main_data': df,
            'column': difference_column,
            'stats_5yr': stats_5yr,
            'selected_years_data': selected_years_data,
            'selected_years': selected_years,
            'percentile_data': df_with_percentiles,
            'anomaly_data': df_with_anomalies,
            'latest_week': latest_data,
            'primary_geography': primary_geography,
            'comparison_geography': comparison_geography,
            'primary_column': df['primary_column'].iloc[0] if not df.empty else '',
            'comparison_column': df['comparison_column'].iloc[0] if not df.empty else '',
            'available_columns': [difference_column]
        }

    def prepare_weekly_change_chart_data(self,
                                       product_type: str = 'all',
                                       geography: str = 'all',
                                       storage_type: str = 'all',
                                       selected_years: List[int] = None,
                                       main_column: str = None) -> Dict:
        """
        Prepare all data needed for weekly change chart visualization.

        Args:
            product_type (str): Product type filter
            geography (str): Geography filter
            storage_type (str): Storage type filter
            selected_years (List[int]): Years to include
            main_column (str): Main column to analyze

        Returns:
            Dict: Complete weekly change chart data package
        """
        # Get the base data (same as main chart)
        df = self.get_filtered_data(product_type, geography, storage_type)

        if df.empty:
            return {'error': 'No data available for the selected filters'}

        # Determine main column if not provided
        if main_column is None:
            numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()
            if not numeric_columns:
                return {'error': 'No numeric columns available'}
            main_column = numeric_columns[0]

        if main_column not in df.columns:
            return {'error': f'Column {main_column} not found in data'}

        # Calculate weekly changes (week-over-week differences)
        df_changes = df.copy()
        df_changes = df_changes.sort_values('Week')

        # Calculate week-over-week change
        change_column = f'{main_column}_weekly_change'
        df_changes[change_column] = df_changes[main_column].diff()

        # Remove the first row (no previous week to compare)
        df_changes = df_changes.dropna(subset=[change_column])

        if df_changes.empty:
            return {'error': 'No weekly change data available'}

        # Calculate all required statistics for the change data
        stats_5yr = self.calculate_5year_stats(df_changes, change_column, selected_years)
        selected_years_data = self.get_selected_years_data(df_changes, change_column, selected_years)
        df_with_percentiles = self.calculate_2025_percentiles(df_changes, change_column)
        df_with_anomalies = self.detect_anomalies(df_with_percentiles, change_column)
        latest_data = self.get_latest_week_data(df_changes, change_column)

        return {
            'main_data': df_changes,
            'column': change_column,
            'original_column': main_column,
            'stats_5yr': stats_5yr,
            'selected_years_data': selected_years_data,
            'selected_years': selected_years,
            'percentile_data': df_with_percentiles,
            'anomaly_data': df_with_anomalies,
            'latest_week': latest_data,
            'available_columns': [change_column],
            'chart_type': 'weekly_change'
        }

    def prepare_seasonal_scenario_chart_data(self,
                                           product_type: str = 'all',
                                           geography: str = 'all',
                                           storage_type: str = 'all',
                                           main_column: str = None,
                                           scenario_years: List[int] = None,
                                           current_week: int = None) -> Dict:
        """
        Prepare seasonal scenario planning chart data.
        Shows actual 2025 data up to current week, then projects using historical seasonality.

        Args:
            product_type (str): Product type filter
            geography (str): Geography filter
            storage_type (str): Storage type filter
            main_column (str): Main column to analyze
            scenario_years (List[int]): Years to use for scenario projection (default: [2023, 2024])
            current_week (int): Current week number (default: auto-detect)

        Returns:
            Dict: Complete seasonal scenario chart data package
        """
        # Get the base data
        df = self.get_filtered_data(product_type, geography, storage_type)

        if df.empty:
            return {'error': 'No data available for the selected filters'}

        # Determine main column if not provided
        if main_column is None:
            numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()
            if not numeric_columns:
                return {'error': 'No numeric columns available'}
            main_column = numeric_columns[0]

        if main_column not in df.columns:
            return {'error': f'Column {main_column} not found in data'}

        # Set default scenario years
        if scenario_years is None:
            scenario_years = [2023, 2024]

        # Determine current week if not provided
        if current_week is None:
            current_week = pd.Timestamp.now().isocalendar().week

        print(f"Seasonal scenario: Current week {current_week}, using years {scenario_years} for projection")

        # Add week and year columns
        df_scenario = df.copy()
        df_scenario['week_of_year'] = df_scenario['Week'].dt.isocalendar().week
        df_scenario['year'] = df_scenario['Week'].dt.year

        # Create pivot table: weeks as index, years as columns
        pivot_data = df_scenario.pivot_table(
            index='week_of_year',
            columns='year',
            values=main_column,
            aggfunc='mean'  # In case of duplicate week/year combinations
        )

        if pivot_data.empty:
            return {'error': 'No data available for pivot analysis'}

        # Ensure we have data for 2025 and scenario years
        available_years = pivot_data.columns.tolist()
        if 2025 not in available_years:
            return {'error': '2025 data not available for scenario analysis'}

        # Filter scenario years to only those available
        valid_scenario_years = [year for year in scenario_years if year in available_years]
        if not valid_scenario_years:
            return {'error': f'None of the scenario years {scenario_years} are available in data'}

        # Create the scenario projection using weekly changes
        scenario_projection = pivot_data[2025].copy()

        # Identify which weeks have 2025 data and which are missing
        weeks_with_2025_data = scenario_projection.dropna().index
        weeks_missing_2025_data = scenario_projection[scenario_projection.isna()].index

        # For weeks missing 2025 data, use weekly changes from scenario years
        if len(weeks_missing_2025_data) > 0 and len(valid_scenario_years) > 0 and len(weeks_with_2025_data) > 0:
            # Get the last actual 2025 value as starting point
            last_2025_week = max(weeks_with_2025_data)
            last_2025_value = scenario_projection.loc[last_2025_week]

            print(f"Seasonal scenario: Last 2025 data point - Week {last_2025_week}: {last_2025_value:.1f}")

            # Calculate weekly changes for scenario years
            scenario_changes = {}
            for year in valid_scenario_years:
                if year in pivot_data.columns:
                    year_data = pivot_data[year].copy()
                    # Calculate week-over-week changes
                    year_changes = year_data.diff()
                    scenario_changes[year] = year_changes

            # Apply changes cumulatively starting from last 2025 value
            current_value = last_2025_value
            projected_values = []
            projected_mins = []
            projected_maxs = []
            projected_means = []

            for week in sorted(weeks_missing_2025_data):
                # Get changes for this week from all scenario years
                week_changes = []
                for year in valid_scenario_years:
                    if year in scenario_changes and week in scenario_changes[year].index:
                        change = scenario_changes[year].loc[week]
                        if pd.notna(change):
                            week_changes.append(change)

                if week_changes:
                    # Calculate statistics for this week's changes
                    avg_change = np.mean(week_changes)
                    min_change = np.min(week_changes)
                    max_change = np.max(week_changes)

                    # Apply average change to get projected value
                    current_value += avg_change
                    scenario_projection.loc[week] = current_value

                    # Calculate min/max projections for bands
                    projected_values.append(current_value)
                    projected_mins.append(current_value - avg_change + min_change)
                    projected_maxs.append(current_value - avg_change + max_change)
                    projected_means.append(current_value)

                    print(f"Week {week}: avg_change={avg_change:.1f}, projected_value={current_value:.1f}")
                else:
                    # No change data available, keep current value
                    scenario_projection.loc[week] = current_value
                    projected_values.append(current_value)
                    projected_mins.append(current_value)
                    projected_maxs.append(current_value)
                    projected_means.append(current_value)

            # Create series for bands (only for missing weeks)
            if projected_mins and projected_maxs:
                scenario_min = pd.Series(projected_mins, index=sorted(weeks_missing_2025_data))
                scenario_max = pd.Series(projected_maxs, index=sorted(weeks_missing_2025_data))
                scenario_mean = pd.Series(projected_means, index=sorted(weeks_missing_2025_data))
            else:
                scenario_min = pd.Series(dtype=float)
                scenario_max = pd.Series(dtype=float)
                scenario_mean = pd.Series(dtype=float)
        else:
            scenario_min = pd.Series(dtype=float)
            scenario_max = pd.Series(dtype=float)
            scenario_mean = pd.Series(dtype=float)

        # Create week dates for 2025 (for x-axis)
        week_dates_2025 = []
        for week in pivot_data.index:
            try:
                # Create date for the given week in 2025
                # Use ISO week format which is more reliable
                date_2025 = datetime.strptime(f'2025-W{int(week):02d}-1', "%Y-W%W-%w")
                week_dates_2025.append(date_2025)
            except Exception as e:
                # Fallback for week 53 or other edge cases
                print(f"Warning: Could not create date for week {week}: {e}")
                # Use a simple approximation: week * 7 days from start of year
                try:
                    start_of_year = datetime(2025, 1, 1)
                    approx_date = start_of_year + timedelta(weeks=int(week)-1)
                    week_dates_2025.append(approx_date)
                except:
                    week_dates_2025.append(datetime(2025, 12, 31))

        return {
            'pivot_data': pivot_data,
            'scenario_projection': scenario_projection,
            'scenario_min': scenario_min,
            'scenario_max': scenario_max,
            'scenario_mean': scenario_mean,
            'week_dates_2025': week_dates_2025,
            'weeks_with_2025_data': weeks_with_2025_data,
            'weeks_missing_2025_data': weeks_missing_2025_data,
            'current_week': current_week,
            'scenario_years': valid_scenario_years,
            'column': main_column,
            'chart_type': 'seasonal_scenario'
        }

    def calculate_3month_correlations(self,
                                    product_type: str = 'all',
                                    geography: str = 'all',
                                    storage_type: str = 'all',
                                    main_column: str = None,
                                    reference_year: int = 2025) -> Dict:
        """
        Calculate 3-month rolling correlations between reference year (2025) and all other years.

        Args:
            product_type (str): Product type filter
            geography (str): Geography filter
            storage_type (str): Storage type filter
            main_column (str): Column to analyze
            reference_year (int): Reference year for correlation (default: 2025)

        Returns:
            Dict: Correlation results with rankings
        """
        try:
            # Get filtered data
            df = self.get_filtered_data(product_type, geography, storage_type)

            if df.empty:
                return {'error': 'No data available for correlation analysis'}

            # Determine main column if not provided
            if main_column is None:
                numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()
                if not numeric_columns:
                    return {'error': 'No numeric columns available'}
                main_column = numeric_columns[0]

            if main_column not in df.columns:
                return {'error': f'Column {main_column} not found in data'}

            # Get current date info for 3-month window calculation
            current_date = pd.Timestamp.now()
            current_week = current_date.isocalendar().week

            # Calculate 3-month window (approximately 13 weeks)
            weeks_in_3months = 13
            start_week = max(1, current_week - weeks_in_3months + 1)
            end_week = current_week

            print(f"Correlation analysis: Current week {current_week}, analyzing weeks {start_week}-{end_week}")

            # Get reference year data (2025)
            df['week_of_year'] = df['Week'].dt.isocalendar().week
            reference_data = df[df['Week'].dt.year == reference_year].copy()

            if reference_data.empty:
                return {'error': f'No data available for reference year {reference_year}'}

            # Filter to 3-month window
            reference_window = reference_data[
                (reference_data['week_of_year'] >= start_week) &
                (reference_data['week_of_year'] <= end_week)
            ].copy()

            if len(reference_window) < 3:
                return {'error': f'Insufficient data in 3-month window for {reference_year}'}

            # Calculate correlations with all other years
            correlations = []
            available_years = sorted(df['Week'].dt.year.unique())

            for year in available_years:
                if year == reference_year:
                    continue

                # Get historical year data for same week window
                year_data = df[df['Week'].dt.year == year].copy()
                year_window = year_data[
                    (year_data['week_of_year'] >= start_week) &
                    (year_data['week_of_year'] <= end_week)
                ].copy()

                if len(year_window) < 3:
                    continue

                # Align data by week of year for correlation
                reference_values = reference_window.set_index('week_of_year')[main_column]
                year_values = year_window.set_index('week_of_year')[main_column]

                # Find common weeks
                common_weeks = reference_values.index.intersection(year_values.index)

                if len(common_weeks) < 3:
                    continue

                # Calculate correlation
                ref_series = reference_values.loc[common_weeks]
                year_series = year_values.loc[common_weeks]

                correlation = ref_series.corr(year_series)

                if not np.isnan(correlation):
                    correlations.append({
                        'year': year,
                        'correlation': correlation,
                        'data_points': len(common_weeks),
                        'weeks_analyzed': f"{min(common_weeks)}-{max(common_weeks)}"
                    })

                    print(f"{reference_year} vs {year}: correlation = {correlation:.3f} ({len(common_weeks)} weeks)")

            # Sort by correlation strength (absolute value for ranking)
            correlations_df = pd.DataFrame(correlations)
            if correlations_df.empty:
                return {'error': 'No valid correlations could be calculated'}

            # Rank by absolute correlation (strongest relationships first)
            correlations_df['abs_correlation'] = correlations_df['correlation'].abs()
            correlations_df = correlations_df.sort_values('abs_correlation', ascending=False)
            correlations_df['rank'] = range(1, len(correlations_df) + 1)

            # Add correlation strength categories
            def categorize_correlation(corr):
                abs_corr = abs(corr)
                if abs_corr >= 0.8:
                    return "Very Strong"
                elif abs_corr >= 0.6:
                    return "Strong"
                elif abs_corr >= 0.4:
                    return "Moderate"
                elif abs_corr >= 0.2:
                    return "Weak"
                else:
                    return "Very Weak"

            correlations_df['strength'] = correlations_df['correlation'].apply(categorize_correlation)
            correlations_df['direction'] = correlations_df['correlation'].apply(lambda x: "Positive" if x > 0 else "Negative")

            return {
                'correlations': correlations_df,
                'reference_year': reference_year,
                'analysis_window': f"Weeks {start_week}-{end_week} ({weeks_in_3months} weeks)",
                'current_week': current_week,
                'column_analyzed': main_column,
                'total_comparisons': len(correlations_df)
            }

        except Exception as e:
            print(f"❌ Error in correlation analysis: {str(e)}")
            return {'error': f'Correlation analysis failed: {str(e)}'}


def create_main_chart(chart_data: Dict) -> go.Figure:
    """
    Create the main inventory chart with 5-year projection bands and selected year overlays.

    Args:
        chart_data (Dict): Prepared chart data

    Returns:
        go.Figure: Plotly figure
    """
    if 'error' in chart_data:
        fig = go.Figure()
        fig.add_annotation(text=chart_data['error'],
                          xref="paper", yref="paper",
                          x=0.5, y=0.5, showarrow=False)
        return fig

    fig = go.Figure()

    df = chart_data['main_data']
    column = chart_data['column']
    stats_5yr = chart_data['stats_5yr']
    selected_years_data = chart_data.get('selected_years_data', pd.DataFrame())
    selected_years = chart_data.get('selected_years', [])

    # Add 5-year projection range band (light blue fill) - always for 2025+
    if not stats_5yr.empty:
        # Create week dates for 2025 (future projection)
        projection_year = 2025
        week_dates = []
        for week in stats_5yr['week_of_year']:
            try:
                # Create date for the given week of projection year (2025)
                date = datetime.strptime(f'{projection_year}-W{week:02d}-1', "%Y-W%W-%w")
                week_dates.append(date)
            except:
                # Fallback for week 53 or other edge cases
                week_dates.append(datetime(projection_year, 12, 31))

        # Add 5-year min/max projection band
        fig.add_trace(go.Scatter(
            x=week_dates + week_dates[::-1],
            y=stats_5yr['max_5yr'].tolist() + stats_5yr['min_5yr'].tolist()[::-1],
            fill='toself',
            fillcolor='rgba(173, 216, 230, 0.3)',  # Light blue
            line=dict(color='rgba(255,255,255,0)'),
            name='5-Year Range (2025 Projection)',
            hoverinfo='skip'
        ))

        # Add 5-year average projection line (dashed blue)
        fig.add_trace(go.Scatter(
            x=week_dates,
            y=stats_5yr['avg_5yr'],
            mode='lines',
            line=dict(color='blue', dash='dash', width=2),
            name='5-Year Average',
            hovertemplate='Week %{x}<br>5-Year Avg: %{y:,.0f}<extra></extra>'
        ))

    # Add selected years as overlay dash lines for comparison (normalized to 2025 timeline)
    if not selected_years_data.empty and selected_years:
        print(f"Adding overlay lines for years: {selected_years}")
        print(f"Selected years data shape: {selected_years_data.shape}")
        print(f"Available years in data: {selected_years_data['year'].unique() if 'year' in selected_years_data.columns else 'No year column'}")

        colors = ['red', 'green', 'orange', 'purple', 'brown']  # Different colors for different years
        for i, year in enumerate(selected_years):
            year_data = selected_years_data[selected_years_data['year'] == year]
            print(f"Year {year} data shape: {year_data.shape}")
            if not year_data.empty and year != 2025:  # Don't show 2025 as overlay since it's already shown as main data
                color = colors[i % len(colors)]
                print(f"Adding trace for {year} with color {color}")

                # Normalize historical year to 2025 timeline for seasonal comparison
                year_data_copy = year_data.copy()
                year_data_copy['week_of_year'] = year_data_copy['Week'].dt.isocalendar().week

                # Create 2025 dates for the same weeks
                normalized_dates = []
                for week_num in year_data_copy['week_of_year']:
                    try:
                        # Create date for the same week in 2025
                        date_2025 = datetime.strptime(f'2025-W{week_num:02d}-1', "%Y-W%W-%w")
                        normalized_dates.append(date_2025)
                    except:
                        # Fallback for week 53 or other edge cases
                        normalized_dates.append(datetime(2025, 12, 31))

                fig.add_trace(go.Scatter(
                    x=normalized_dates,  # Use 2025 timeline
                    y=year_data_copy[column],
                    mode='lines',
                    line=dict(color=color, dash='dot', width=3),  # Thicker line for visibility
                    name=f'{year} Seasonal Pattern',
                    hovertemplate=f'{year} Pattern: Week %{{customdata}}<br>Value: %{{y:,.0f}}<extra></extra>',
                    customdata=year_data_copy['week_of_year']
                ))
            elif year == 2025:
                print(f"Skipping {year} overlay (already shown as main data)")
            else:
                print(f"No data found for year {year}")
    else:
        print(f"No overlay lines: selected_years_data.empty={selected_years_data.empty}, selected_years={selected_years}")

    # Add current/all data line (solid black)
    fig.add_trace(go.Scatter(
        x=df['Week'],
        y=df[column],
        mode='lines+markers',
        line=dict(color='black', width=2),
        marker=dict(size=3),
        name='Historical Data',
        hovertemplate='%{x}<br>Value: %{y:,.0f}<extra></extra>'
    ))

    # Update layout
    fig.update_layout(
        title=f'Global Liquids Inventory: {column}<br><sub>Blue shading: 2025 projection | Dotted lines: Historical seasonal patterns overlaid on 2025 timeline</sub>',
        xaxis_title='2025 Timeline (Historical patterns normalized to same weeks)',
        yaxis_title='Inventory Level',
        hovermode='x unified',
        showlegend=True,
        height=500,
        template='plotly_white'
    )

    return fig


def create_cross_geographic_chart(chart_data: Dict) -> go.Figure:
    """
    Create cross-geographic analysis chart showing difference between two regions.

    Args:
        chart_data (Dict): Prepared cross-geographic chart data

    Returns:
        go.Figure: Plotly figure
    """
    if 'error' in chart_data:
        fig = go.Figure()
        fig.add_annotation(text=chart_data['error'],
                          xref="paper", yref="paper",
                          x=0.5, y=0.5, showarrow=False)
        return fig

    fig = go.Figure()

    df = chart_data['main_data']
    column = chart_data['column']
    stats_5yr = chart_data['stats_5yr']
    selected_years_data = chart_data.get('selected_years_data', pd.DataFrame())
    selected_years = chart_data.get('selected_years', [])
    primary_geo = chart_data.get('primary_geography', '')
    comparison_geo = chart_data.get('comparison_geography', '')

    # Add 5-year projection range band for differences
    if not stats_5yr.empty:
        projection_year = 2025
        week_dates = []
        for week in stats_5yr['week_of_year']:
            try:
                date = datetime.strptime(f'{projection_year}-W{week:02d}-1', "%Y-W%W-%w")
                week_dates.append(date)
            except:
                week_dates.append(datetime(projection_year, 12, 31))

        # Add 5-year min/max projection band
        fig.add_trace(go.Scatter(
            x=week_dates + week_dates[::-1],
            y=stats_5yr['max_5yr'].tolist() + stats_5yr['min_5yr'].tolist()[::-1],
            fill='toself',
            fillcolor='rgba(173, 216, 230, 0.3)',  # Light blue
            line=dict(color='rgba(255,255,255,0)'),
            name='5-Year Range (Difference)',
            hoverinfo='skip'
        ))

        # Add 5-year average difference line
        fig.add_trace(go.Scatter(
            x=week_dates,
            y=stats_5yr['avg_5yr'],
            mode='lines',
            line=dict(color='blue', dash='dash', width=2),
            name='5-Year Average Difference',
            hovertemplate='Week %{x}<br>5-Year Avg Difference: %{y:,.0f}<extra></extra>'
        ))

    # Add zero reference line
    if not df.empty:
        fig.add_hline(y=0, line_dash="solid", line_color="gray", line_width=1,
                     annotation_text="Zero Line", annotation_position="bottom right")

    # Add 2025 actual difference data
    df_2025 = df[df['Week'].dt.year == 2025].copy()
    if not df_2025.empty:
        # Color code based on positive/negative differences
        colors = ['green' if val >= 0 else 'red' for val in df_2025[column]]

        fig.add_trace(go.Scatter(
            x=df_2025['Week'],
            y=df_2025[column],
            mode='lines+markers',
            line=dict(color='black', width=3),
            marker=dict(color=colors, size=6),
            name=f'2025 Difference ({primary_geo.upper()} - {comparison_geo.upper()})',
            hovertemplate=f'Week: %{{x}}<br>Difference: %{{y:,.0f}}<br>' +
                         f'{primary_geo.upper()} - {comparison_geo.upper()}<extra></extra>'
        ))

    # Add selected years as overlay lines
    if not selected_years_data.empty and selected_years:
        colors = ['red', 'green', 'orange', 'purple', 'brown']
        for i, year in enumerate(selected_years):
            year_data = selected_years_data[selected_years_data['year'] == year]
            if not year_data.empty and year != 2025:
                color = colors[i % len(colors)]

                # Normalize to 2025 timeline
                year_data_copy = year_data.copy()
                year_data_copy['week_of_year'] = year_data_copy['Week'].dt.isocalendar().week

                normalized_dates = []
                for week_num in year_data_copy['week_of_year']:
                    try:
                        date_2025 = datetime.strptime(f'2025-W{week_num:02d}-1', "%Y-W%W-%w")
                        normalized_dates.append(date_2025)
                    except:
                        normalized_dates.append(datetime(2025, 12, 31))

                fig.add_trace(go.Scatter(
                    x=normalized_dates,
                    y=year_data_copy[column],
                    mode='lines',
                    line=dict(color=color, dash='dot', width=2),
                    name=f'{year} Difference Pattern',
                    hovertemplate=f'{year} Difference: %{{y:,.0f}}<br>Week %{{customdata}}<extra></extra>',
                    customdata=year_data_copy['week_of_year']
                ))

    # Update layout
    fig.update_layout(
        title=f'Cross-Geographic Analysis: {primary_geo.upper()} vs {comparison_geo.upper()}',
        xaxis_title='Week (2025 Timeline)',
        yaxis_title=f'Inventory Difference ({primary_geo.upper()} - {comparison_geo.upper()})',
        template='plotly_white',
        hovermode='x unified',
        legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
        height=500
    )

    return fig


def create_weekly_change_chart(chart_data: Dict) -> go.Figure:
    """
    Create weekly change chart showing week-over-week differences with historical comparison.

    Args:
        chart_data (Dict): Prepared weekly change chart data

    Returns:
        go.Figure: Plotly figure
    """
    if 'error' in chart_data:
        fig = go.Figure()
        fig.add_annotation(text=chart_data['error'],
                          xref="paper", yref="paper",
                          x=0.5, y=0.5, showarrow=False)
        return fig

    fig = go.Figure()

    df = chart_data['main_data']
    column = chart_data['column']
    original_column = chart_data.get('original_column', '')
    stats_5yr = chart_data['stats_5yr']
    selected_years_data = chart_data.get('selected_years_data', pd.DataFrame())
    selected_years = chart_data.get('selected_years', [])

    # Add 5-year projection range band for weekly changes
    if not stats_5yr.empty:
        projection_year = 2025
        week_dates = []
        for week in stats_5yr['week_of_year']:
            try:
                date = datetime.strptime(f'{projection_year}-W{week:02d}-1', "%Y-W%W-%w")
                week_dates.append(date)
            except:
                week_dates.append(datetime(projection_year, 12, 31))

        # Add 5-year min/max projection band
        fig.add_trace(go.Scatter(
            x=week_dates + week_dates[::-1],
            y=stats_5yr['max_5yr'].tolist() + stats_5yr['min_5yr'].tolist()[::-1],
            fill='toself',
            fillcolor='rgba(173, 216, 230, 0.3)',  # Light blue
            line=dict(color='rgba(255,255,255,0)'),
            name='5-Year Change Range',
            hoverinfo='skip'
        ))

        # Add 5-year average change line
        fig.add_trace(go.Scatter(
            x=week_dates,
            y=stats_5yr['avg_5yr'],
            mode='lines',
            line=dict(color='blue', dash='dash', width=2),
            name='5-Year Average Change',
            hovertemplate='Week %{x}<br>5-Year Avg Change: %{y:,.0f}<extra></extra>'
        ))

    # Add zero reference line (no change)
    if not df.empty:
        fig.add_hline(y=0, line_dash="solid", line_color="gray", line_width=1,
                     annotation_text="No Change", annotation_position="bottom right")

    # Add 2025 actual weekly changes
    df_2025 = df[df['Week'].dt.year == 2025].copy()
    if not df_2025.empty:
        # Color code based on positive/negative changes
        colors = ['green' if val >= 0 else 'red' for val in df_2025[column]]

        fig.add_trace(go.Scatter(
            x=df_2025['Week'],
            y=df_2025[column],
            mode='lines+markers',
            line=dict(color='black', width=3),
            marker=dict(color=colors, size=6),
            name='2025 Weekly Changes',
            hovertemplate=f'Week: %{{x}}<br>Weekly Change: %{{y:,.0f}}<br>' +
                         f'(Week-over-Week Difference)<extra></extra>'
        ))

    # Add selected years as overlay lines
    if not selected_years_data.empty and selected_years:
        colors = ['red', 'green', 'orange', 'purple', 'brown']
        for i, year in enumerate(selected_years):
            year_data = selected_years_data[selected_years_data['year'] == year]
            if not year_data.empty and year != 2025:
                color = colors[i % len(colors)]

                # Normalize to 2025 timeline
                year_data_copy = year_data.copy()
                year_data_copy['week_of_year'] = year_data_copy['Week'].dt.isocalendar().week

                normalized_dates = []
                for week_num in year_data_copy['week_of_year']:
                    try:
                        date_2025 = datetime.strptime(f'2025-W{week_num:02d}-1', "%Y-W%W-%w")
                        normalized_dates.append(date_2025)
                    except:
                        normalized_dates.append(datetime(2025, 12, 31))

                fig.add_trace(go.Scatter(
                    x=normalized_dates,
                    y=year_data_copy[column],
                    mode='lines',
                    line=dict(color=color, dash='dot', width=2),
                    name=f'{year} Change Pattern',
                    hovertemplate=f'{year} Weekly Change: %{{y:,.0f}}<br>Week %{{customdata}}<extra></extra>',
                    customdata=year_data_copy['week_of_year']
                ))

    # Update layout
    fig.update_layout(
        title=f'2025 Weekly Changes with Historical Comparison ({original_column})',
        xaxis_title='Week (2025 Timeline)',
        yaxis_title=f'Weekly Change (Week-over-Week Difference)',
        template='plotly_white',
        hovermode='x unified',
        legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
        height=500
    )

    return fig


def create_seasonal_scenario_chart(chart_data: Dict) -> go.Figure:
    """
    Create seasonal scenario planning chart showing actual 2025 data up to current week,
    then projecting forward using historical seasonal patterns.

    Args:
        chart_data (Dict): Prepared seasonal scenario chart data

    Returns:
        go.Figure: Plotly figure
    """
    if 'error' in chart_data:
        fig = go.Figure()
        fig.add_annotation(text=chart_data['error'],
                          xref="paper", yref="paper",
                          x=0.5, y=0.5, showarrow=False)
        return fig

    fig = go.Figure()

    scenario_projection = chart_data['scenario_projection']
    scenario_min = chart_data['scenario_min']
    scenario_max = chart_data['scenario_max']
    scenario_mean = chart_data['scenario_mean']
    week_dates_2025 = chart_data['week_dates_2025']
    weeks_with_2025_data = chart_data['weeks_with_2025_data']
    weeks_missing_2025_data = chart_data['weeks_missing_2025_data']
    current_week = chart_data['current_week']
    scenario_years = chart_data['scenario_years']
    column = chart_data['column']

    # Add scenario band (min/max range from historical years) - ONLY for missing 2025 data
    if not scenario_min.empty and not scenario_max.empty:
        # Create dates only for missing weeks
        missing_week_dates = []
        for week in weeks_missing_2025_data:
            try:
                date_2025 = datetime.strptime(f'2025-W{int(week):02d}-1', "%Y-W%W-%w")
                missing_week_dates.append(date_2025)
            except Exception as e:
                try:
                    start_of_year = datetime(2025, 1, 1)
                    approx_date = start_of_year + timedelta(weeks=int(week)-1)
                    missing_week_dates.append(approx_date)
                except:
                    missing_week_dates.append(datetime(2025, 12, 31))

        if missing_week_dates:
            fig.add_trace(go.Scatter(
                x=missing_week_dates + missing_week_dates[::-1],
                y=scenario_max.tolist() + scenario_min.tolist()[::-1],
                fill='toself',
                fillcolor='rgba(173, 216, 230, 0.3)',  # Light blue
                line=dict(color='rgba(255,255,255,0)'),
                name=f'Projection Range ({", ".join(map(str, scenario_years))})',
                hoverinfo='skip'
            ))

            # Add scenario mean line (dashed) - ONLY for missing weeks
            fig.add_trace(go.Scatter(
                x=missing_week_dates,
                y=scenario_mean.tolist(),
                mode='lines',
                line=dict(color='blue', dash='dash', width=2),
                name=f'Historical Average ({", ".join(map(str, scenario_years))})',
                hovertemplate='Week %{x}<br>Historical Avg: %{y:,.0f}<extra></extra>'
            ))

    # Create mapping from week numbers to dates
    week_to_date = {week: date for week, date in zip(scenario_projection.index, week_dates_2025)}

    # Split 2025 data into actual (has data) vs projected (missing data, filled with historical average)
    actual_weeks = weeks_with_2025_data
    projected_weeks = weeks_missing_2025_data

    # Add 2025 actual data (solid line) - where we have real 2025 data
    if len(actual_weeks) > 0:
        actual_dates = [week_to_date[week] for week in actual_weeks if week in week_to_date]
        actual_values = scenario_projection.loc[actual_weeks]

        if len(actual_dates) == len(actual_values):
            fig.add_trace(go.Scatter(
                x=actual_dates,
                y=actual_values,
                mode='lines+markers',
                line=dict(color='black', width=4),
                marker=dict(color='black', size=6),
                name='2025 Actual Data',
                hovertemplate='2025 Actual: %{y:,.0f}<br>Week %{customdata}<extra></extra>',
                customdata=actual_weeks
            ))

    # Note: 2025 projected line removed since historical average line already shows the projection

    # Create description based on data availability
    if len(weeks_missing_2025_data) > 0:
        missing_weeks_str = f"weeks {min(weeks_missing_2025_data)}-{max(weeks_missing_2025_data)}"
        if len(weeks_with_2025_data) > 0:
            last_week = max(weeks_with_2025_data)
            description = f"2025 actual data through week {last_week} | Blue lines show projection for {missing_weeks_str} using {', '.join(map(str, scenario_years))} weekly changes"
        else:
            description = f"Blue lines show projection for {missing_weeks_str} using {', '.join(map(str, scenario_years))} weekly changes"
    else:
        description = "Complete 2025 actual data available"

    # Update layout
    fig.update_layout(
        title=f'2025 Seasonal Scenario Planning ({column})',
        xaxis_title='Week (2025 Timeline)',
        yaxis_title=f'{column}',
        template='plotly_white',
        hovermode='x unified',
        legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
        height=500,
        annotations=[
            dict(
                text=description,
                xref="paper", yref="paper",
                x=0.5, y=-0.15, showarrow=False,
                font=dict(size=12, color="gray")
            )
        ]
    )

    return fig


def create_percentile_trend_chart(chart_data: Dict) -> go.Figure:
    """
    Create a weekly percentile trend chart showing 2025 data against historical distribution.
    Much more insightful than the ribbon format.

    Args:
        chart_data (Dict): Prepared chart data

    Returns:
        go.Figure: Plotly figure
    """
    print(f"create_percentile_trend_chart: Starting trend chart creation")

    if 'error' in chart_data:
        fig = go.Figure()
        fig.add_annotation(text=chart_data['error'],
                          xref="paper", yref="paper",
                          x=0.5, y=0.5, showarrow=False)
        return fig

    df = chart_data['percentile_data']
    column = chart_data['column']

    print(f"create_percentile_trend_chart: Column: {column}")
    print(f"create_percentile_trend_chart: Data shape: {df.shape}")

    if df.empty or 'percentile' not in df.columns:
        fig = go.Figure()
        fig.add_annotation(text="No 2025 data available for percentile analysis",
                          xref="paper", yref="paper",
                          x=0.5, y=0.5, showarrow=False)
        return fig

    # Sort by week of year for proper trend display
    df_sorted = df.sort_values('week_of_year').reset_index(drop=True)

    print(f"create_percentile_trend_chart: Week range: {df_sorted['week_of_year'].min()} to {df_sorted['week_of_year'].max()}")
    print(f"create_percentile_trend_chart: Percentile range: {df_sorted['percentile'].min():.1f}% to {df_sorted['percentile'].max():.1f}%")

    fig = go.Figure()

    # Add percentile reference zones (background shading)
    fig.add_hrect(y0=0, y1=25, fillcolor="red", opacity=0.1, line_width=0, annotation_text="Very Low (0-25%)", annotation_position="top left")
    fig.add_hrect(y0=25, y1=50, fillcolor="orange", opacity=0.1, line_width=0, annotation_text="Below Average (25-50%)", annotation_position="top left")
    fig.add_hrect(y0=50, y1=75, fillcolor="yellow", opacity=0.1, line_width=0, annotation_text="Above Average (50-75%)", annotation_position="top left")
    fig.add_hrect(y0=75, y1=100, fillcolor="green", opacity=0.1, line_width=0, annotation_text="Very High (75-100%)", annotation_position="top left")

    # Add reference lines for key percentiles
    for percentile, color, name in [(25, 'red', '25th Percentile'), (50, 'gray', '50th Percentile (Median)'), (75, 'green', '75th Percentile')]:
        fig.add_hline(y=percentile, line=dict(color=color, width=1, dash='dot'),
                     annotation_text=name, annotation_position="right")

    # Add 2025 percentile trend line
    fig.add_trace(go.Scatter(
        x=df_sorted['week_of_year'],
        y=df_sorted['percentile'],
        mode='lines+markers',
        line=dict(color='blue', width=3),
        marker=dict(size=8, color='blue', line=dict(width=2, color='white')),
        name='2025 Percentile Trend',
        hovertemplate='Week %{x}<br>2025 Percentile: %{y:.1f}%<br>Value: %{customdata:,.0f}<br>Interpretation: %{text}<extra></extra>',
        customdata=df_sorted[column],
        text=[f"{'Very Low' if p < 25 else 'Below Avg' if p < 50 else 'Above Avg' if p < 75 else 'Very High'}" for p in df_sorted['percentile']]
    ))

    # Calculate trend statistics
    avg_percentile = df_sorted['percentile'].mean()
    trend_slope = (df_sorted['percentile'].iloc[-1] - df_sorted['percentile'].iloc[0]) / len(df_sorted) if len(df_sorted) > 1 else 0

    # Update layout
    fig.update_layout(
        title=f'2025 Weekly Inventory Percentile Trend: {column}<br><sub>Average: {avg_percentile:.1f}% | Trend: {"Improving" if trend_slope > 0 else "Declining" if trend_slope < 0 else "Stable"}</sub>',
        xaxis=dict(
            title='Week of Year',
            range=[0, 52],
            dtick=4,
            showgrid=True,
            gridcolor='lightgray'
        ),
        yaxis=dict(
            title='Percentile Ranking (%)',
            range=[0, 100],
            dtick=25,
            showgrid=True,
            gridcolor='lightgray'
        ),
        height=400,
        showlegend=True,
        template='plotly_white',
        margin=dict(l=60, r=60, t=80, b=60)
    )

    return fig


def create_anomaly_flags(chart_data: Dict) -> go.Figure:
    """
    Create anomaly flag indicators.

    Args:
        chart_data (Dict): Prepared chart data

    Returns:
        go.Figure: Plotly figure with anomaly flags
    """
    if 'error' in chart_data:
        fig = go.Figure()
        fig.add_annotation(text=chart_data['error'],
                          xref="paper", yref="paper",
                          x=0.5, y=0.5, showarrow=False)
        return fig

    df = chart_data['anomaly_data']

    if df.empty or 'is_anomaly' not in df.columns:
        fig = go.Figure()
        fig.add_annotation(text="No anomaly data available",
                          xref="paper", yref="paper",
                          x=0.5, y=0.5, showarrow=False)
        return fig

    fig = go.Figure()

    # Filter for anomalies only
    anomalies = df[df['is_anomaly']].copy()

    if not anomalies.empty:
        # Group by week of year for flag positioning
        weekly_anomalies = anomalies.groupby('week_of_year').agg({
            'is_anomaly_high': 'any',
            'is_anomaly_low': 'any',
            'Week': 'last'
        }).reset_index()

        # Create flag markers
        for _, row in weekly_anomalies.iterrows():
            color = 'red' if row['is_anomaly_high'] else 'orange'
            symbol = 'triangle-up' if row['is_anomaly_high'] else 'triangle-down'
            flag_type = 'High' if row['is_anomaly_high'] else 'Low'

            fig.add_trace(go.Scatter(
                x=[row['week_of_year']],
                y=[1],
                mode='markers',
                marker=dict(
                    symbol=symbol,
                    size=15,
                    color=color,
                    line=dict(width=2, color='white')
                ),
                name=f'{flag_type} Anomaly',
                hovertemplate=f'Week %{{x}}<br>{flag_type} Anomaly Flag<extra></extra>'
            ))

    # Update layout
    fig.update_layout(
        title='Anomaly Flags (Top/Bottom 10%)',
        xaxis_title='Week of Year',
        yaxis=dict(visible=False, range=[0.5, 1.5]),
        height=100,
        showlegend=True,
        template='plotly_white',
        margin=dict(l=50, r=50, t=50, b=30)
    )

    return fig


def get_summary_stats(chart_data: Dict) -> Dict:
    """
    Generate summary statistics for the dashboard.

    Args:
        chart_data (Dict): Prepared chart data

    Returns:
        Dict: Summary statistics
    """
    if 'error' in chart_data:
        return {'error': chart_data['error']}

    df = chart_data['main_data']
    column = chart_data['column']
    latest_data = chart_data['latest_week']
    anomaly_data = chart_data['anomaly_data']

    stats = {}

    # Basic statistics
    if not df.empty and column in df.columns:
        stats['current_value'] = latest_data.get('value', 'N/A')
        stats['current_week'] = latest_data.get('week', 'N/A')
        stats['total_weeks'] = len(df)
        stats['data_range'] = f"{df['Week'].min().strftime('%Y-%m-%d')} to {df['Week'].max().strftime('%Y-%m-%d')}"

        # Calculate recent trends (last 4 weeks)
        recent_data = df.tail(4)
        if len(recent_data) >= 2:
            trend = recent_data[column].iloc[-1] - recent_data[column].iloc[0]
            stats['recent_trend'] = 'Increasing' if trend > 0 else 'Decreasing' if trend < 0 else 'Stable'
            stats['trend_value'] = abs(trend)

        # Anomaly count
        if not anomaly_data.empty and 'is_anomaly' in anomaly_data.columns:
            stats['total_anomalies'] = anomaly_data['is_anomaly'].sum()
            stats['high_anomalies'] = anomaly_data['is_anomaly_high'].sum()
            stats['low_anomalies'] = anomaly_data['is_anomaly_low'].sum()

    return stats
