#!/usr/bin/env python3
"""
Sample Energy Futures Data Generator

This script generates realistic sample energy futures data for testing purposes.
Creates weekly data from 2018-2025 with realistic price movements and correlations.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

def generate_sample_energy_futures_data():
    """Generate realistic sample energy futures data."""
    
    # Create date range (weekly, Friday close)
    start_date = '2018-01-05'  # First Friday of 2018
    end_date = '2025-12-26'    # Last Friday of 2025
    
    dates = pd.date_range(start=start_date, end=end_date, freq='W-FRI')
    
    # Initialize random seed for reproducible data
    np.random.seed(42)
    
    # Base prices (realistic starting points)
    base_prices = {
        'WTI': 60.0,      # $/barrel
        'Brent': 65.0,    # $/barrel  
        'RBOB': 1.80,     # $/gallon
        'ULSD': 1.90,     # $/gallon
    }
    
    # Generate price series with realistic volatility and trends
    data = pd.DataFrame(index=dates)
    
    # WTI Crude Oil (base commodity)
    wti_returns = np.random.normal(0, 0.03, len(dates))  # 3% weekly volatility
    wti_trend = np.sin(np.arange(len(dates)) * 2 * np.pi / 52) * 0.01  # Seasonal trend
    data['WTI'] = base_prices['WTI'] * np.exp(np.cumsum(wti_returns + wti_trend))
    
    # Brent Crude Oil (correlated with WTI but with spread)
    brent_spread = np.random.normal(5, 2, len(dates))  # $5 average spread with $2 volatility
    data['Brent'] = data['WTI'] + brent_spread
    
    # RBOB Gasoline (seasonal patterns, higher in summer)
    rbob_base_spread = 0.20  # Base spread over crude
    seasonal_rbob = 0.15 * np.sin(np.arange(len(dates)) * 2 * np.pi / 52 - np.pi/2)  # Peak in summer
    rbob_noise = np.random.normal(0, 0.05, len(dates))
    data['RBOB'] = (data['WTI'] / 42) + rbob_base_spread + seasonal_rbob + rbob_noise  # Convert $/barrel to $/gallon
    
    # ULSD Diesel (less seasonal than gasoline)
    ulsd_base_spread = 0.25  # Base spread over crude
    seasonal_ulsd = 0.10 * np.sin(np.arange(len(dates)) * 2 * np.pi / 52 + np.pi/4)  # Peak in winter
    ulsd_noise = np.random.normal(0, 0.04, len(dates))
    data['ULSD'] = (data['WTI'] / 42) + ulsd_base_spread + seasonal_ulsd + ulsd_noise
    
    # Add some realistic price shocks (COVID, geopolitical events)
    covid_start = pd.Timestamp('2020-03-01')
    covid_end = pd.Timestamp('2020-06-01')
    covid_mask = (data.index >= covid_start) & (data.index <= covid_end)
    data.loc[covid_mask, 'WTI'] *= 0.3  # 70% price drop during COVID
    data.loc[covid_mask, 'Brent'] *= 0.4  # 60% price drop
    
    # Ukraine conflict impact (2022)
    ukraine_start = pd.Timestamp('2022-02-01')
    ukraine_end = pd.Timestamp('2022-12-01')
    ukraine_mask = (data.index >= ukraine_start) & (data.index <= ukraine_end)
    data.loc[ukraine_mask, 'WTI'] *= 1.4  # 40% price increase
    data.loc[ukraine_mask, 'Brent'] *= 1.5  # 50% price increase
    
    # Calculate spreads
    data['RBOB_Crack'] = data['RBOB'] - (data['WTI'] / 42)  # Convert WTI to $/gallon
    data['ULSD_Crack'] = data['ULSD'] - (data['WTI'] / 42)
    data['Brent_WTI_Spread'] = data['Brent'] - data['WTI']
    data['Crack_3_2_1'] = (2 * data['RBOB'] + data['ULSD']) / 3 - (data['WTI'] / 42)
    
    # Add technical indicators
    for instrument in ['WTI', 'Brent', 'RBOB', 'ULSD']:
        # Moving averages
        data[f'{instrument}_MA4'] = data[instrument].rolling(window=4).mean()
        data[f'{instrument}_MA13'] = data[instrument].rolling(window=13).mean()
        data[f'{instrument}_MA52'] = data[instrument].rolling(window=52).mean()
        
        # Returns and volatility
        data[f'{instrument}_Return'] = data[instrument].pct_change() * 100
        data[f'{instrument}_Vol4'] = data[f'{instrument}_Return'].rolling(window=4).std()
    
    # Add metadata
    data['Data_Source'] = 'Sample Data Generator'
    data['Last_Updated'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # Ensure no negative prices
    price_columns = ['WTI', 'Brent', 'RBOB', 'ULSD']
    for col in price_columns:
        data[col] = data[col].clip(lower=0.1)
    
    return data

def main():
    """Generate and save sample energy futures data."""
    print("🚀 Generating sample energy futures data...")
    
    # Generate data
    data = generate_sample_energy_futures_data()
    
    # Ensure data folder exists
    os.makedirs('data', exist_ok=True)
    
    # Save to CSV
    output_path = 'data/energy_futures_weekly.csv'
    data.to_csv(output_path, index=True, float_format='%.4f')
    
    print(f"✅ Generated {len(data)} weeks of sample data")
    print(f"📁 Saved to: {output_path}")
    print(f"📅 Date range: {data.index.min().strftime('%Y-%m-%d')} to {data.index.max().strftime('%Y-%m-%d')}")
    
    # Print summary statistics
    print("\n📊 Sample Data Summary:")
    print("="*50)
    latest = data.iloc[-1]
    print(f"Latest WTI:   ${latest['WTI']:.2f}/barrel")
    print(f"Latest Brent: ${latest['Brent']:.2f}/barrel")
    print(f"Latest RBOB:  ${latest['RBOB']:.3f}/gallon")
    print(f"Latest ULSD:  ${latest['ULSD']:.3f}/gallon")
    print(f"Brent-WTI:    ${latest['Brent_WTI_Spread']:.2f}")
    print(f"RBOB Crack:   ${latest['RBOB_Crack']:.3f}")
    print(f"ULSD Crack:   ${latest['ULSD_Crack']:.3f}")
    print("="*50)
    
    print("\n🎯 This sample data includes:")
    print("  • Realistic price movements and volatility")
    print("  • Seasonal patterns (gasoline peaks in summer)")
    print("  • Historical events (COVID crash, Ukraine conflict)")
    print("  • Calculated crack spreads and technical indicators")
    print("  • Weekly data from 2018-2025")
    
    print(f"\n✨ You can now use this data in your dashboard!")

if __name__ == "__main__":
    main()
