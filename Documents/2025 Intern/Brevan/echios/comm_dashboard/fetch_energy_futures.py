#!/usr/bin/env python3
"""
Energy Futures Data Fetcher

This script downloads energy futures data from Yahoo Finance and stores it as CSV files.
Covers WTI, Brent, RBOB, and ULSD futures from 2018-2025 with calculated crack spreads.

Data Sources:
- WTI Crude Futures (CL=F)
- Brent Crude Futures (BZ=F) 
- RBOB Gasoline Futures (RB=F)
- ULSD Diesel Futures (HO=F)

Calculated Metrics:
- RBOB Crack Spread (RBOB - WTI)
- ULSD Crack Spread (ULSD - WTI)
- Brent-WTI Spread (Brent - WTI)

Output: Weekly data (Friday close) saved to data/energy_futures_weekly.csv
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import logging
from typing import Dict, Optional
import warnings

# Import configuration
try:
    from energy_futures_config import *
except ImportError:
    # Fallback configuration if config file not found
    YAHOO_TICKERS = {
        'WTI': 'CL=F', 'Brent': 'BZ=F', 'RBOB': 'RB=F', 'ULSD': 'HO=F'
    }
    DEFAULT_START_DATE = '2018-01-01'
    DATA_FOLDER = 'data'
    OUTPUT_FILENAME = 'energy_futures_weekly.csv'
    ENABLE_TECHNICAL_INDICATORS = True
    ENABLE_SPREAD_CALCULATIONS = True

# Configure logging
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL if 'LOG_LEVEL' in globals() else 'INFO'),
    format=LOG_FORMAT if 'LOG_FORMAT' in globals() else '%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Suppress warnings
warnings.filterwarnings('ignore', category=FutureWarning)

class EnergyFuturesDataFetcher:
    """Fetches and processes energy futures data from Yahoo Finance."""
    
    def __init__(self, data_folder: Optional[str] = None):
        """
        Initialize the data fetcher.

        Args:
            data_folder (str): Folder to save CSV files (default: from config)
        """
        self.data_folder = data_folder or DATA_FOLDER
        self.tickers = YAHOO_TICKERS.copy()

        # Ensure data folder exists
        os.makedirs(self.data_folder, exist_ok=True)

        logger.info(f"Initialized EnergyFuturesDataFetcher")
        logger.info(f"Data folder: {self.data_folder}")
        logger.info(f"Tickers: {list(self.tickers.keys())}")
        
    def fetch_daily_data(self, start_date: str = '2018-01-01', end_date: Optional[str] = None, max_retries: int = 3) -> pd.DataFrame:
        """
        Fetch daily futures data from Yahoo Finance with retry logic.

        Args:
            start_date (str): Start date in YYYY-MM-DD format
            end_date (str): End date in YYYY-MM-DD format (default: today)
            max_retries (int): Maximum number of retry attempts

        Returns:
            pd.DataFrame: Daily futures prices
        """
        if end_date is None:
            end_date = datetime.now().strftime('%Y-%m-%d')

        logger.info(f"Fetching daily data from {start_date} to {end_date}")

        # Try fetching data with retries
        for attempt in range(max_retries):
            try:
                logger.info(f"Attempt {attempt + 1}/{max_retries}")

                # Download data for all tickers
                data = yf.download(
                    list(self.tickers.values()),
                    start=start_date,
                    end=end_date,
                    interval='1d',
                    group_by='ticker',
                    auto_adjust=True,
                    prepost=False,  # Disable prepost to reduce load
                    threads=False,  # Disable threading to avoid rate limits
                    progress=False  # Disable progress bar
                )

                if data.empty:
                    logger.warning(f"No data retrieved on attempt {attempt + 1}")
                    if attempt < max_retries - 1:
                        import time
                        time.sleep(10)  # Wait 10 seconds before retry
                        continue
                    else:
                        logger.error("No data retrieved after all attempts")
                        return pd.DataFrame()

                # Extract adjusted close prices
                daily_prices = pd.DataFrame()

                for name, ticker in self.tickers.items():
                    try:
                        if len(self.tickers) == 1:
                            # Single ticker case
                            daily_prices[name] = data['Close']
                        else:
                            # Multiple tickers case
                            daily_prices[name] = data[ticker]['Close']
                        logger.info(f"✓ Successfully fetched {name} ({ticker})")
                    except KeyError as e:
                        logger.warning(f"⚠️ Could not fetch {name} ({ticker}): {e}")
                        continue

                # Remove rows with all NaN values
                daily_prices = daily_prices.dropna(how='all')

                if not daily_prices.empty:
                    logger.info(f"Fetched {len(daily_prices)} daily records")
                    return daily_prices
                else:
                    logger.warning(f"No valid data extracted on attempt {attempt + 1}")
                    if attempt < max_retries - 1:
                        import time
                        time.sleep(10)
                        continue

            except Exception as e:
                logger.warning(f"Error on attempt {attempt + 1}: {e}")
                if attempt < max_retries - 1:
                    import time
                    time.sleep(10)
                    continue
                else:
                    logger.error(f"Failed after {max_retries} attempts: {e}")

        return pd.DataFrame()
    
    def convert_to_weekly(self, daily_data: pd.DataFrame) -> pd.DataFrame:
        """
        Convert daily data to weekly data (Friday close).
        
        Args:
            daily_data (pd.DataFrame): Daily futures prices
            
        Returns:
            pd.DataFrame: Weekly futures prices
        """
        logger.info("Converting daily data to weekly (Friday close)")
        
        # Resample to weekly, taking the last value of each week (Friday close)
        weekly_data = daily_data.resample('W-FRI').last()
        
        # Forward fill missing values (in case of holidays)
        weekly_data = weekly_data.fillna(method='ffill')
        
        # Remove rows with all NaN values
        weekly_data = weekly_data.dropna(how='all')
        
        logger.info(f"Converted to {len(weekly_data)} weekly records")
        return weekly_data
    
    def calculate_spreads(self, weekly_data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate crack spreads and other derived metrics.
        
        Args:
            weekly_data (pd.DataFrame): Weekly futures prices
            
        Returns:
            pd.DataFrame: Weekly data with calculated spreads
        """
        logger.info("Calculating crack spreads and derived metrics")
        
        data_with_spreads = weekly_data.copy()
        
        # Calculate crack spreads (product - crude)
        if 'RBOB' in data_with_spreads.columns and 'WTI' in data_with_spreads.columns:
            data_with_spreads['RBOB_Crack'] = data_with_spreads['RBOB'] - data_with_spreads['WTI']
            logger.info("✓ Calculated RBOB Crack Spread")
        
        if 'ULSD' in data_with_spreads.columns and 'WTI' in data_with_spreads.columns:
            data_with_spreads['ULSD_Crack'] = data_with_spreads['ULSD'] - data_with_spreads['WTI']
            logger.info("✓ Calculated ULSD Crack Spread")
        
        # Calculate Brent-WTI spread
        if 'Brent' in data_with_spreads.columns and 'WTI' in data_with_spreads.columns:
            data_with_spreads['Brent_WTI_Spread'] = data_with_spreads['Brent'] - data_with_spreads['WTI']
            logger.info("✓ Calculated Brent-WTI Spread")
        
        # Calculate 3:2:1 crack spread approximation (3 barrels crude -> 2 barrels gasoline + 1 barrel distillate)
        if all(col in data_with_spreads.columns for col in ['RBOB', 'ULSD', 'WTI']):
            data_with_spreads['Crack_3_2_1'] = (2 * data_with_spreads['RBOB'] + data_with_spreads['ULSD']) / 3 - data_with_spreads['WTI']
            logger.info("✓ Calculated 3:2:1 Crack Spread")
        
        return data_with_spreads
    
    def add_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Add technical indicators and rolling statistics.
        
        Args:
            data (pd.DataFrame): Weekly futures data
            
        Returns:
            pd.DataFrame: Data with technical indicators
        """
        logger.info("Adding technical indicators")
        
        enhanced_data = data.copy()
        
        # Add rolling averages for key instruments
        for instrument in ['WTI', 'Brent', 'RBOB', 'ULSD']:
            if instrument in enhanced_data.columns:
                # 4-week (monthly) moving average
                enhanced_data[f'{instrument}_MA4'] = enhanced_data[instrument].rolling(window=4).mean()
                
                # 13-week (quarterly) moving average  
                enhanced_data[f'{instrument}_MA13'] = enhanced_data[instrument].rolling(window=13).mean()
                
                # 52-week (annual) moving average
                enhanced_data[f'{instrument}_MA52'] = enhanced_data[instrument].rolling(window=52).mean()
                
                # Weekly returns (percentage change)
                enhanced_data[f'{instrument}_Return'] = enhanced_data[instrument].pct_change() * 100
                
                # 4-week volatility (rolling standard deviation of returns)
                enhanced_data[f'{instrument}_Vol4'] = enhanced_data[f'{instrument}_Return'].rolling(window=4).std()
        
        logger.info("✓ Added moving averages, returns, and volatility indicators")
        return enhanced_data
    
    def save_to_csv(self, data: pd.DataFrame, filename: str = 'energy_futures_weekly.csv') -> str:
        """
        Save data to CSV file.
        
        Args:
            data (pd.DataFrame): Data to save
            filename (str): Output filename
            
        Returns:
            str: Full path to saved file
        """
        filepath = os.path.join(self.data_folder, filename)
        
        # Add metadata columns
        data_with_metadata = data.copy()
        data_with_metadata['Data_Source'] = 'Yahoo Finance'
        data_with_metadata['Last_Updated'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # Save to CSV
        data_with_metadata.to_csv(filepath, index=True)
        
        logger.info(f"✅ Saved {len(data_with_metadata)} records to {filepath}")
        return filepath
    
    def run_full_pipeline(self, start_date: str = '2018-01-01', end_date: Optional[str] = None) -> str:
        """
        Run the complete data fetching and processing pipeline.
        
        Args:
            start_date (str): Start date in YYYY-MM-DD format
            end_date (str): End date in YYYY-MM-DD format (default: today)
            
        Returns:
            str: Path to saved CSV file
        """
        logger.info("🚀 Starting Energy Futures Data Pipeline")
        
        # Step 1: Fetch daily data
        daily_data = self.fetch_daily_data(start_date, end_date)
        if daily_data.empty:
            raise ValueError("Failed to fetch daily data")
        
        # Step 2: Convert to weekly
        weekly_data = self.convert_to_weekly(daily_data)
        
        # Step 3: Calculate spreads
        data_with_spreads = self.calculate_spreads(weekly_data)
        
        # Step 4: Add technical indicators
        enhanced_data = self.add_technical_indicators(data_with_spreads)
        
        # Step 5: Save to CSV
        output_path = self.save_to_csv(enhanced_data)
        
        # Print summary
        self.print_summary(enhanced_data)
        
        logger.info("✅ Pipeline completed successfully")
        return output_path
    
    def print_summary(self, data: pd.DataFrame) -> None:
        """Print summary statistics of the fetched data."""
        logger.info("\n" + "="*60)
        logger.info("📊 DATA SUMMARY")
        logger.info("="*60)
        logger.info(f"Date Range: {data.index.min().strftime('%Y-%m-%d')} to {data.index.max().strftime('%Y-%m-%d')}")
        logger.info(f"Total Weeks: {len(data)}")
        logger.info(f"Columns: {len(data.columns)}")
        
        logger.info("\n📈 Latest Prices ($/barrel or $/gallon):")
        latest = data.iloc[-1]
        for instrument in ['WTI', 'Brent', 'RBOB', 'ULSD']:
            if instrument in data.columns:
                logger.info(f"  {instrument}: ${latest[instrument]:.2f}")
        
        logger.info("\n🔧 Latest Spreads:")
        for spread in ['RBOB_Crack', 'ULSD_Crack', 'Brent_WTI_Spread', 'Crack_3_2_1']:
            if spread in data.columns:
                logger.info(f"  {spread}: ${latest[spread]:.2f}")
        
        logger.info("="*60)


def main():
    """Main execution function."""
    try:
        # Initialize fetcher
        fetcher = EnergyFuturesDataFetcher()
        
        # Run pipeline for 2018-2025 data
        output_path = fetcher.run_full_pipeline(
            start_date='2018-01-01',
            end_date=None  # Will use today's date
        )
        
        print(f"\n🎉 Success! Energy futures data saved to: {output_path}")
        print("\nYou can now use this data in your inventory dashboard for:")
        print("  • WTI and Brent crude oil prices")
        print("  • RBOB gasoline and ULSD diesel futures")
        print("  • Crack spreads and technical indicators")
        print("  • Weekly price movements and volatility")
        
    except Exception as e:
        logger.error(f"❌ Pipeline failed: {e}")
        raise


if __name__ == "__main__":
    main()
